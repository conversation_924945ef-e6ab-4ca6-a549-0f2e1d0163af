<view class="house-list-page data-v-145608e6"><view class="filter-bar data-v-145608e6"><view data-event-opts="{{[['tap',[['showAreaFilter',['$event']]]]]}}" class="filter-item data-v-145608e6" bindtap="__e"><text class="filter-text data-v-145608e6">{{selectedArea||'区域'}}</text><uni-icons vue-id="766b94e0-1" type="down" size="12" color="#999" class="data-v-145608e6" bind:__l="__l"></uni-icons></view><view data-event-opts="{{[['tap',[['showPriceFilter',['$event']]]]]}}" class="filter-item data-v-145608e6" bindtap="__e"><text class="filter-text data-v-145608e6">{{selectedPrice||'价格'}}</text><uni-icons vue-id="766b94e0-2" type="down" size="12" color="#999" class="data-v-145608e6" bind:__l="__l"></uni-icons></view><view data-event-opts="{{[['tap',[['showTypeFilter',['$event']]]]]}}" class="filter-item data-v-145608e6" bindtap="__e"><text class="filter-text data-v-145608e6">{{selectedType||'类型'}}</text><uni-icons vue-id="766b94e0-3" type="down" size="12" color="#999" class="data-v-145608e6" bind:__l="__l"></uni-icons></view><view data-event-opts="{{[['tap',[['showSortFilter',['$event']]]]]}}" class="filter-item data-v-145608e6" bindtap="__e"><text class="filter-text data-v-145608e6">{{selectedSort||'排序'}}</text><uni-icons vue-id="766b94e0-4" type="down" size="12" color="#999" class="data-v-145608e6" bind:__l="__l"></uni-icons></view></view><scroll-view class="house-scroll data-v-145608e6" scroll-y="{{true}}" refresher-enabled="{{true}}" refresher-triggered="{{refreshing}}" data-event-opts="{{[['scrolltolower',[['loadMore',['$event']]]],['refresherrefresh',[['onRefresh',['$event']]]]]}}" bindscrolltolower="__e" bindrefresherrefresh="__e"><view class="house-list data-v-145608e6"><block wx:for="{{houseList}}" wx:for-item="house" wx:for-index="__i0__" wx:key="_id"><view data-event-opts="{{[['tap',[['goToDetail',['$0'],[[['houseList','_id',house._id,'_id']]]]]]]}}" class="house-item data-v-145608e6" bindtap="__e"><image class="house-image data-v-145608e6" src="{{house.images[0]}}" mode="aspectFill"></image><view class="house-info data-v-145608e6"><view class="house-title data-v-145608e6">{{house.title}}</view><view class="house-desc data-v-145608e6">{{house.description}}</view><view class="house-tags data-v-145608e6"><block wx:for="{{house.tags}}" wx:for-item="tag" wx:for-index="__i1__" wx:key="*this"><text class="tag data-v-145608e6">{{tag}}</text></block></view><view class="house-bottom data-v-145608e6"><view class="price data-v-145608e6"><text class="price-num data-v-145608e6">{{house.price}}</text><text class="price-unit data-v-145608e6">元/月</text></view><view class="location data-v-145608e6"><uni-icons vue-id="{{'766b94e0-5-'+__i0__}}" type="location" size="12" color="#999" class="data-v-145608e6" bind:__l="__l"></uni-icons><text class="location-text data-v-145608e6">{{house.address}}</text></view></view></view><view data-event-opts="{{[['tap',[['toggleFavorite',['$0'],[[['houseList','_id',house._id]]]]]]]}}" class="favorite-btn data-v-145608e6" catchtap="__e"><uni-icons vue-id="{{'766b94e0-6-'+__i0__}}" type="{{house.is_favorite?'heart-filled':'heart'}}" size="20" color="{{house.is_favorite?'#FF4D4F':'#999'}}" class="data-v-145608e6" bind:__l="__l"></uni-icons></view></view></block></view><block wx:if="{{$root.g0>0}}"><view class="loading-more data-v-145608e6"><uni-load-more vue-id="766b94e0-7" status="{{loadingStatus}}" class="data-v-145608e6" bind:__l="__l"></uni-load-more></view></block><block wx:if="{{$root.g1}}"><view class="empty-state data-v-145608e6"><image class="empty-image data-v-145608e6" src="/static/empty/no-house.png"></image><text class="empty-text data-v-145608e6">暂无房源信息</text></view></block></scroll-view><uni-popup vue-id="766b94e0-8" type="bottom" data-ref="filterPopup" class="data-v-145608e6 vue-ref" bind:__l="__l" vue-slots="{{['default']}}"><view class="filter-popup data-v-145608e6"><view class="popup-header data-v-145608e6"><text class="popup-title data-v-145608e6">筛选条件</text><view data-event-opts="{{[['tap',[['closeFilter',['$event']]]]]}}" class="popup-close data-v-145608e6" bindtap="__e"><uni-icons vue-id="{{('766b94e0-9')+','+('766b94e0-8')}}" type="close" size="18" color="#999" class="data-v-145608e6" bind:__l="__l"></uni-icons></view></view><view class="popup-content data-v-145608e6"><block wx:if="{{currentFilter==='area'}}"><view class="data-v-145608e6"><view class="filter-group data-v-145608e6"><text class="group-title data-v-145608e6">选择区域</text><view class="option-list data-v-145608e6"><block wx:for="{{areaOptions}}" wx:for-item="area" wx:for-index="__i2__" wx:key="value"><view data-event-opts="{{[['tap',[['selectArea',['$0'],[[['areaOptions','value',area.value]]]]]]]}}" class="{{['option-item','data-v-145608e6',(selectedArea===area.label)?'active':'']}}" bindtap="__e">{{''+area.label+''}}</view></block></view></view></view></block><block wx:if="{{currentFilter==='price'}}"><view class="data-v-145608e6"><view class="filter-group data-v-145608e6"><text class="group-title data-v-145608e6">价格范围</text><view class="option-list data-v-145608e6"><block wx:for="{{priceOptions}}" wx:for-item="price" wx:for-index="__i3__" wx:key="value"><view data-event-opts="{{[['tap',[['selectPrice',['$0'],[[['priceOptions','value',price.value]]]]]]]}}" class="{{['option-item','data-v-145608e6',(selectedPrice===price.label)?'active':'']}}" bindtap="__e">{{''+price.label+''}}</view></block></view></view></view></block><block wx:if="{{currentFilter==='type'}}"><view class="data-v-145608e6"><view class="filter-group data-v-145608e6"><text class="group-title data-v-145608e6">房源类型</text><view class="option-list data-v-145608e6"><block wx:for="{{typeOptions}}" wx:for-item="type" wx:for-index="__i4__" wx:key="value"><view data-event-opts="{{[['tap',[['selectType',['$0'],[[['typeOptions','value',type.value]]]]]]]}}" class="{{['option-item','data-v-145608e6',(selectedType===type.label)?'active':'']}}" bindtap="__e">{{''+type.label+''}}</view></block></view></view></view></block><block wx:if="{{currentFilter==='sort'}}"><view class="data-v-145608e6"><view class="filter-group data-v-145608e6"><text class="group-title data-v-145608e6">排序方式</text><view class="option-list data-v-145608e6"><block wx:for="{{sortOptions}}" wx:for-item="sort" wx:for-index="__i5__" wx:key="value"><view data-event-opts="{{[['tap',[['selectSort',['$0'],[[['sortOptions','value',sort.value]]]]]]]}}" class="{{['option-item','data-v-145608e6',(selectedSort===sort.label)?'active':'']}}" bindtap="__e">{{''+sort.label+''}}</view></block></view></view></view></block></view><view class="popup-footer data-v-145608e6"><button data-event-opts="{{[['tap',[['resetFilter',['$event']]]]]}}" class="reset-btn data-v-145608e6" bindtap="__e">重置</button><button data-event-opts="{{[['tap',[['confirmFilter',['$event']]]]]}}" class="confirm-btn data-v-145608e6" bindtap="__e">确定</button></view></view></uni-popup></view>
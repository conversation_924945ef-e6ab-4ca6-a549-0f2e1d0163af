<template>
  <view class="search-page">
    <!-- 搜索栏 -->
    <view class="search-bar">
      <view class="search-input">
        <uni-icons type="search" size="18" color="#999"></uni-icons>
        <input 
          class="input" 
          v-model="searchKeyword" 
          placeholder="搜索房源、地址、小区"
          confirm-type="search"
          @confirm="onSearch"
          focus
        />
        <view class="clear-btn" @click="clearSearch" v-if="searchKeyword">
          <uni-icons type="clear" size="16" color="#999"></uni-icons>
        </view>
      </view>
      <text class="cancel-btn" @click="goBack">取消</text>
    </view>

    <!-- 搜索历史 -->
    <view class="history-section" v-if="!searchKeyword && searchHistory.length > 0">
      <view class="section-header">
        <text class="section-title">搜索历史</text>
        <view class="clear-history" @click="clearHistory">
          <uni-icons type="trash" size="16" color="#999"></uni-icons>
        </view>
      </view>
      <view class="history-list">
        <view 
          class="history-item" 
          v-for="(item, index) in searchHistory" 
          :key="index"
          @click="searchByHistory(item)"
        >
          <uni-icons type="clock" size="14" color="#999"></uni-icons>
          <text class="history-text">{{ item }}</text>
        </view>
      </view>
    </view>

    <!-- 热门搜索 -->
    <view class="hot-section" v-if="!searchKeyword">
      <view class="section-title">热门搜索</view>
      <view class="hot-list">
        <view 
          class="hot-item" 
          v-for="(item, index) in hotSearchList" 
          :key="index"
          @click="searchByHot(item)"
        >
          {{ item }}
        </view>
      </view>
    </view>

    <!-- 搜索建议 -->
    <view class="suggest-section" v-if="searchKeyword && suggestList.length > 0">
      <view class="suggest-list">
        <view 
          class="suggest-item" 
          v-for="(item, index) in suggestList" 
          :key="index"
          @click="searchBySuggest(item)"
        >
          <uni-icons type="search" size="16" color="#999"></uni-icons>
          <text class="suggest-text">{{ item }}</text>
        </view>
      </view>
    </view>

    <!-- 搜索结果 -->
    <view class="result-section" v-if="showResult">
      <view class="result-header">
        <text class="result-count">找到 {{ resultCount }} 个房源</text>
      </view>
      
      <scroll-view 
        class="result-scroll" 
        scroll-y 
        @scrolltolower="loadMore"
      >
        <view class="result-list">
          <house-card
            v-for="house in resultList"
            :key="house._id"
            :house-data="house"
            @click="goToDetail"
            @favorite="toggleFavorite"
          ></house-card>
        </view>

        <!-- 加载状态 -->
        <view class="loading-more" v-if="resultList.length > 0">
          <uni-load-more :status="loadingStatus"></uni-load-more>
        </view>

        <!-- 空状态 -->
        <view class="empty-state" v-if="!loading && resultList.length === 0 && showResult">
          <image class="empty-image" src="/static/empty/no-search.png"></image>
          <text class="empty-text">没有找到相关房源</text>
          <text class="empty-desc">试试其他关键词吧</text>
        </view>
      </scroll-view>
    </view>
  </view>
</template>

<script>
import { houseAPI } from '@/utils/request.js'
import HouseCard from '@/components/house-card/house-card.vue'

export default {
  components: {
    HouseCard
  },
  
  data() {
    return {
      searchKeyword: '',
      loading: false,
      loadingStatus: 'more',
      currentPage: 1,
      pageSize: 10,
      hasMore: true,
      showResult: false,
      resultCount: 0,
      
      searchHistory: [],
      suggestList: [],
      resultList: [],
      
      hotSearchList: [
        '整租', '合租', '一居室', '二居室', '三居室',
        '地铁房', '精装修', '拎包入住', '短租', '月租'
      ]
    }
  },
  
  onLoad() {
    this.loadSearchHistory()
  },
  
  watch: {
    searchKeyword(newVal) {
      if (newVal) {
        this.getSuggestList(newVal)
      } else {
        this.suggestList = []
        this.showResult = false
      }
    }
  },
  
  methods: {
    // 加载搜索历史
    loadSearchHistory() {
      const history = uni.getStorageSync('searchHistory') || []
      this.searchHistory = history.slice(0, 10) // 最多显示10条
    },
    
    // 保存搜索历史
    saveSearchHistory(keyword) {
      let history = uni.getStorageSync('searchHistory') || []
      
      // 移除重复项
      history = history.filter(item => item !== keyword)
      
      // 添加到开头
      history.unshift(keyword)
      
      // 最多保存20条
      history = history.slice(0, 20)
      
      uni.setStorageSync('searchHistory', history)
      this.searchHistory = history.slice(0, 10)
    },
    
    // 清除搜索历史
    clearHistory() {
      uni.showModal({
        title: '提示',
        content: '确定要清除搜索历史吗？',
        success: (res) => {
          if (res.confirm) {
            uni.removeStorageSync('searchHistory')
            this.searchHistory = []
          }
        }
      })
    },
    
    // 获取搜索建议
    getSuggestList(keyword) {
      // 模拟搜索建议
      const suggests = [
        `${keyword} 整租`,
        `${keyword} 合租`,
        `${keyword} 一居室`,
        `${keyword} 二居室`,
        `${keyword} 地铁房`
      ]
      this.suggestList = suggests.slice(0, 5)
    },
    
    // 执行搜索
    async onSearch() {
      if (!this.searchKeyword.trim()) return
      
      this.saveSearchHistory(this.searchKeyword.trim())
      this.performSearch(this.searchKeyword.trim())
    },
    
    // 通过历史记录搜索
    searchByHistory(keyword) {
      this.searchKeyword = keyword
      this.performSearch(keyword)
    },
    
    // 通过热门搜索
    searchByHot(keyword) {
      this.searchKeyword = keyword
      this.saveSearchHistory(keyword)
      this.performSearch(keyword)
    },
    
    // 通过建议搜索
    searchBySuggest(keyword) {
      this.searchKeyword = keyword
      this.saveSearchHistory(keyword)
      this.performSearch(keyword)
    },
    
    // 执行搜索请求
    async performSearch(keyword, loadMore = false) {
      try {
        this.loading = true
        
        if (!loadMore) {
          this.currentPage = 1
          this.hasMore = true
          this.showResult = true
        }
        
        const params = {
          keyword,
          page: this.currentPage,
          limit: this.pageSize,
          status: 'approved'
        }
        
        const result = await houseAPI.getHouseList(params)
        const newList = result.data.list || []
        
        if (loadMore) {
          this.resultList.push(...newList)
        } else {
          this.resultList = newList
          this.resultCount = result.data.total || newList.length
        }
        
        this.hasMore = newList.length === this.pageSize
        this.loadingStatus = this.hasMore ? 'more' : 'noMore'
        
      } catch (error) {
        console.error('搜索失败：', error)
        this.loadingStatus = 'error'
      } finally {
        this.loading = false
      }
    },
    
    // 加载更多
    loadMore() {
      if (this.hasMore && !this.loading) {
        this.currentPage++
        this.loadingStatus = 'loading'
        this.performSearch(this.searchKeyword, true)
      }
    },
    
    // 清除搜索
    clearSearch() {
      this.searchKeyword = ''
      this.showResult = false
      this.suggestList = []
    },
    
    // 返回
    goBack() {
      uni.navigateBack()
    },
    
    // 跳转到详情
    goToDetail(house) {
      uni.navigateTo({
        url: `/pages/house/detail?id=${house._id}`
      })
    },
    
    // 切换收藏
    async toggleFavorite(house) {
      try {
        if (house.is_favorite) {
          await houseAPI.unfavoriteHouse(house._id)
          house.is_favorite = false
          uni.showToast({
            title: '已取消收藏',
            icon: 'none'
          })
        } else {
          await houseAPI.favoriteHouse(house._id)
          house.is_favorite = true
          uni.showToast({
            title: '收藏成功',
            icon: 'none'
          })
        }
      } catch (error) {
        console.error('收藏操作失败：', error)
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.search-page {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: $bg-color-page;
}

/* 搜索栏 */
.search-bar {
  display: flex;
  align-items: center;
  padding: $spacing-md;
  background-color: #fff;
  border-bottom: 1px solid $border-color-light;
  
  .search-input {
    flex: 1;
    display: flex;
    align-items: center;
    height: 72rpx;
    padding: 0 $spacing-md;
    background-color: $bg-color-light;
    border-radius: 36rpx;
    margin-right: $spacing-md;
    
    .input {
      flex: 1;
      margin-left: $spacing-xs;
      font-size: 28rpx;
      color: $text-color-primary;
    }
    
    .clear-btn {
      width: 32rpx;
      height: 32rpx;
      display: flex;
      align-items: center;
      justify-content: center;
    }
  }
  
  .cancel-btn {
    font-size: 28rpx;
    color: $text-color-secondary;
  }
}

/* 搜索历史 */
.history-section {
  background-color: #fff;
  padding: $spacing-lg;
  margin-bottom: $spacing-md;
  
  .section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: $spacing-md;
    
    .section-title {
      font-size: 28rpx;
      font-weight: 600;
      color: $text-color-primary;
    }
    
    .clear-history {
      width: 60rpx;
      height: 60rpx;
      display: flex;
      align-items: center;
      justify-content: center;
    }
  }
  
  .history-list {
    .history-item {
      display: flex;
      align-items: center;
      padding: $spacing-sm 0;
      border-bottom: 1px solid $border-color-lighter;
      
      &:last-child {
        border-bottom: none;
      }
      
      .history-text {
        margin-left: $spacing-sm;
        font-size: 26rpx;
        color: $text-color-secondary;
      }
    }
  }
}

/* 热门搜索 */
.hot-section {
  background-color: #fff;
  padding: $spacing-lg;
  margin-bottom: $spacing-md;
  
  .section-title {
    font-size: 28rpx;
    font-weight: 600;
    color: $text-color-primary;
    margin-bottom: $spacing-md;
  }
  
  .hot-list {
    display: flex;
    flex-wrap: wrap;
    gap: $spacing-md;
    
    .hot-item {
      padding: $spacing-sm $spacing-md;
      background-color: $bg-color-light;
      color: $text-color-secondary;
      font-size: 26rpx;
      border-radius: $border-radius-base;
    }
  }
}

/* 搜索建议 */
.suggest-section {
  background-color: #fff;
  
  .suggest-list {
    .suggest-item {
      display: flex;
      align-items: center;
      padding: $spacing-md $spacing-lg;
      border-bottom: 1px solid $border-color-lighter;
      
      &:last-child {
        border-bottom: none;
      }
      
      .suggest-text {
        margin-left: $spacing-sm;
        font-size: 28rpx;
        color: $text-color-primary;
      }
    }
  }
}

/* 搜索结果 */
.result-section {
  flex: 1;
  display: flex;
  flex-direction: column;
  
  .result-header {
    background-color: #fff;
    padding: $spacing-md $spacing-lg;
    border-bottom: 1px solid $border-color-light;
    
    .result-count {
      font-size: 26rpx;
      color: $text-color-secondary;
    }
  }
  
  .result-scroll {
    flex: 1;
  }
  
  .result-list {
    padding: $spacing-md;
    
    .house-card {
      margin-bottom: $spacing-md;
    }
  }
}

/* 加载状态 */
.loading-more {
  padding: $spacing-lg;
}

/* 空状态 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 120rpx $spacing-lg;
  
  .empty-image {
    width: 200rpx;
    height: 200rpx;
    margin-bottom: $spacing-lg;
  }
  
  .empty-text {
    font-size: 28rpx;
    color: $text-color-secondary;
    margin-bottom: $spacing-sm;
  }
  
  .empty-desc {
    font-size: 24rpx;
    color: $text-color-tertiary;
  }
}
</style>

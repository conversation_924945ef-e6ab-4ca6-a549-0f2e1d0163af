<template>
  <view class="publish-page">
    <form @submit="submitForm">
      <!-- 房源图片 -->
      <view class="form-section">
        <view class="section-title">房源图片 <text class="required">*</text></view>
        <view class="image-upload">
          <view class="image-list">
            <view 
              class="image-item" 
              v-for="(image, index) in formData.images" 
              :key="index"
            >
              <image class="uploaded-image" :src="image" mode="aspectFill"></image>
              <view class="delete-btn" @click="deleteImage(index)">
                <uni-icons type="close" size="16" color="#fff"></uni-icons>
              </view>
            </view>
            <view 
              class="upload-btn" 
              @click="chooseImage"
              v-if="formData.images.length < 9"
            >
              <uni-icons type="camera" size="32" color="#999"></uni-icons>
              <text class="upload-text">添加图片</text>
              <text class="upload-count">{{ formData.images.length }}/9</text>
            </view>
          </view>
        </view>
      </view>

      <!-- 基本信息 -->
      <view class="form-section">
        <view class="section-title">基本信息</view>
        
        <view class="form-item">
          <text class="label">房源标题 <text class="required">*</text></text>
          <input 
            class="input" 
            v-model="formData.title" 
            placeholder="请输入房源标题"
            maxlength="50"
          />
        </view>
        
        <view class="form-item">
          <text class="label">房源类型 <text class="required">*</text></text>
          <picker 
            mode="selector" 
            :range="typeOptions" 
            range-key="label"
            @change="onTypeChange"
          >
            <view class="picker">
              {{ selectedType || '请选择房源类型' }}
              <uni-icons type="right" size="14" color="#999"></uni-icons>
            </view>
          </picker>
        </view>
        
        <view class="form-item">
          <text class="label">租金 <text class="required">*</text></text>
          <view class="price-input">
            <input 
              class="input" 
              v-model="formData.price" 
              placeholder="请输入租金"
              type="number"
            />
            <text class="unit">元/月</text>
          </view>
        </view>
        
        <view class="form-item">
          <text class="label">房源地址 <text class="required">*</text></text>
          <view class="address-input" @click="chooseLocation">
            <text class="address-text" :class="{ placeholder: !formData.address }">
              {{ formData.address || '请选择房源地址' }}
            </text>
            <uni-icons type="right" size="14" color="#999"></uni-icons>
          </view>
        </view>
      </view>

      <!-- 房源配置 -->
      <view class="form-section">
        <view class="section-title">房源配置</view>
        
        <view class="form-item">
          <text class="label">面积</text>
          <view class="area-input">
            <input 
              class="input" 
              v-model="formData.area" 
              placeholder="请输入面积"
              type="number"
            />
            <text class="unit">㎡</text>
          </view>
        </view>
        
        <view class="form-item">
          <text class="label">楼层</text>
          <input 
            class="input" 
            v-model="formData.floor" 
            placeholder="如：5/10层"
          />
        </view>
        
        <view class="form-item">
          <text class="label">朝向</text>
          <picker 
            mode="selector" 
            :range="orientationOptions"
            @change="onOrientationChange"
          >
            <view class="picker">
              {{ formData.orientation || '请选择朝向' }}
              <uni-icons type="right" size="14" color="#999"></uni-icons>
            </view>
          </picker>
        </view>
        
        <view class="form-item">
          <text class="label">装修</text>
          <picker 
            mode="selector" 
            :range="decorationOptions"
            @change="onDecorationChange"
          >
            <view class="picker">
              {{ formData.decoration || '请选择装修情况' }}
              <uni-icons type="right" size="14" color="#999"></uni-icons>
            </view>
          </picker>
        </view>
      </view>

      <!-- 房源描述 -->
      <view class="form-section">
        <view class="section-title">房源描述</view>
        <textarea 
          class="textarea" 
          v-model="formData.description" 
          placeholder="请详细描述房源情况，如周边配套、交通等"
          maxlength="500"
        ></textarea>
        <view class="char-count">{{ formData.description.length }}/500</view>
      </view>

      <!-- 房源标签 -->
      <view class="form-section">
        <view class="section-title">房源标签</view>
        <view class="tag-list">
          <view 
            class="tag-item" 
            v-for="tag in tagOptions" 
            :key="tag"
            :class="{ active: formData.tags.includes(tag) }"
            @click="toggleTag(tag)"
          >
            {{ tag }}
          </view>
        </view>
      </view>

      <!-- 联系方式 -->
      <view class="form-section">
        <view class="section-title">联系方式</view>
        <view class="form-item">
          <text class="label">联系电话</text>
          <input 
            class="input" 
            v-model="formData.contact_phone" 
            placeholder="请输入联系电话"
            type="number"
          />
        </view>
      </view>

      <!-- 提交按钮 -->
      <view class="submit-section">
        <button 
          class="submit-btn" 
          :disabled="submitting"
          @click="submitForm"
        >
          {{ submitting ? '发布中...' : '发布房源' }}
        </button>
      </view>
    </form>
  </view>
</template>

<script>
import { houseAPI, uploadAPI } from '@/utils/request.js'

export default {
  data() {
    return {
      submitting: false,
      formData: {
        title: '',
        type: '',
        price: '',
        address: '',
        area: '',
        floor: '',
        orientation: '',
        decoration: '',
        description: '',
        contact_phone: '',
        images: [],
        tags: []
      },
      
      typeOptions: [
        { label: '整租', value: 'whole' },
        { label: '合租', value: 'shared' },
        { label: '短租', value: 'short' }
      ],
      
      orientationOptions: [
        '南北', '东西', '南', '北', '东', '西', 
        '东南', '西南', '东北', '西北'
      ],
      
      decorationOptions: [
        '毛坯', '简装', '精装', '豪装'
      ],
      
      tagOptions: [
        '近地铁', '拎包入住', '独立卫浴', '有阳台', 
        '有空调', '有洗衣机', '有冰箱', '有电梯',
        '停车位', '宽带', '可做饭', '可洗澡'
      ]
    }
  },
  
  computed: {
    selectedType() {
      const type = this.typeOptions.find(item => item.value === this.formData.type)
      return type ? type.label : ''
    }
  },
  
  methods: {
    // 选择图片
    chooseImage() {
      const remainCount = 9 - this.formData.images.length
      uni.chooseImage({
        count: remainCount,
        sizeType: ['compressed'],
        sourceType: ['album', 'camera'],
        success: async (res) => {
          uni.showLoading({
            title: '上传中...'
          })
          
          try {
            for (let filePath of res.tempFilePaths) {
              const fileId = await uploadAPI.uploadImage(filePath)
              this.formData.images.push(fileId)
            }
          } catch (error) {
            console.error('图片上传失败：', error)
          } finally {
            uni.hideLoading()
          }
        }
      })
    },
    
    // 删除图片
    deleteImage(index) {
      this.formData.images.splice(index, 1)
    },
    
    // 选择房源类型
    onTypeChange(e) {
      const index = e.detail.value
      this.formData.type = this.typeOptions[index].value
    },
    
    // 选择朝向
    onOrientationChange(e) {
      const index = e.detail.value
      this.formData.orientation = this.orientationOptions[index]
    },
    
    // 选择装修
    onDecorationChange(e) {
      const index = e.detail.value
      this.formData.decoration = this.decorationOptions[index]
    },
    
    // 选择位置
    chooseLocation() {
      uni.chooseLocation({
        success: (res) => {
          this.formData.address = res.address
          this.formData.latitude = res.latitude
          this.formData.longitude = res.longitude
        }
      })
    },
    
    // 切换标签
    toggleTag(tag) {
      const index = this.formData.tags.indexOf(tag)
      if (index > -1) {
        this.formData.tags.splice(index, 1)
      } else {
        this.formData.tags.push(tag)
      }
    },
    
    // 表单验证
    validateForm() {
      if (!this.formData.title.trim()) {
        uni.showToast({
          title: '请输入房源标题',
          icon: 'none'
        })
        return false
      }
      
      if (!this.formData.type) {
        uni.showToast({
          title: '请选择房源类型',
          icon: 'none'
        })
        return false
      }
      
      if (!this.formData.price) {
        uni.showToast({
          title: '请输入租金',
          icon: 'none'
        })
        return false
      }
      
      if (!this.formData.address) {
        uni.showToast({
          title: '请选择房源地址',
          icon: 'none'
        })
        return false
      }
      
      if (this.formData.images.length === 0) {
        uni.showToast({
          title: '请至少上传一张图片',
          icon: 'none'
        })
        return false
      }
      
      return true
    },
    
    // 提交表单
    async submitForm() {
      if (!this.validateForm()) {
        return
      }
      
      try {
        this.submitting = true
        
        await houseAPI.publishHouse(this.formData)
        
        uni.showToast({
          title: '发布成功',
          icon: 'success'
        })
        
        // 跳转到我的发布页面
        setTimeout(() => {
          uni.redirectTo({
            url: '/pages/publish/my'
          })
        }, 1500)
        
      } catch (error) {
        console.error('发布房源失败：', error)
      } finally {
        this.submitting = false
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.publish-page {
  background-color: $bg-color-page;
  padding-bottom: $spacing-xl;
}

/* 表单区块 */
.form-section {
  background-color: #fff;
  margin-bottom: $spacing-md;
  padding: $spacing-lg;

  .section-title {
    font-size: 32rpx;
    font-weight: 600;
    color: $text-color-primary;
    margin-bottom: $spacing-lg;

    .required {
      color: $error-color;
    }
  }
}

/* 图片上传 */
.image-upload {
  .image-list {
    display: flex;
    flex-wrap: wrap;
    gap: $spacing-md;

    .image-item {
      position: relative;
      width: 200rpx;
      height: 200rpx;
      border-radius: $border-radius-base;
      overflow: hidden;

      .uploaded-image {
        width: 100%;
        height: 100%;
      }

      .delete-btn {
        position: absolute;
        top: 8rpx;
        right: 8rpx;
        width: 40rpx;
        height: 40rpx;
        background-color: rgba(0, 0, 0, 0.5);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
      }
    }

    .upload-btn {
      width: 200rpx;
      height: 200rpx;
      border: 2rpx dashed $border-color-base;
      border-radius: $border-radius-base;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      background-color: $bg-color-light;

      .upload-text {
        font-size: 24rpx;
        color: $text-color-tertiary;
        margin-top: $spacing-xs;
      }

      .upload-count {
        font-size: 20rpx;
        color: $text-color-quaternary;
        margin-top: 4rpx;
      }
    }
  }
}

/* 表单项 */
.form-item {
  margin-bottom: $spacing-lg;

  &:last-child {
    margin-bottom: 0;
  }

  .label {
    display: block;
    font-size: 28rpx;
    color: $text-color-primary;
    margin-bottom: $spacing-sm;

    .required {
      color: $error-color;
    }
  }

  .input {
    width: 100%;
    height: 80rpx;
    padding: 0 $spacing-md;
    background-color: $bg-color-light;
    border: 1px solid $border-color-light;
    border-radius: $border-radius-base;
    font-size: 28rpx;
    color: $text-color-primary;

    &::placeholder {
      color: $text-color-quaternary;
    }
  }

  .picker {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 80rpx;
    padding: 0 $spacing-md;
    background-color: $bg-color-light;
    border: 1px solid $border-color-light;
    border-radius: $border-radius-base;
    font-size: 28rpx;
    color: $text-color-primary;
  }

  .price-input,
  .area-input {
    display: flex;
    align-items: center;

    .input {
      flex: 1;
      margin-right: $spacing-sm;
    }

    .unit {
      font-size: 28rpx;
      color: $text-color-secondary;
    }
  }

  .address-input {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 80rpx;
    padding: 0 $spacing-md;
    background-color: $bg-color-light;
    border: 1px solid $border-color-light;
    border-radius: $border-radius-base;

    .address-text {
      font-size: 28rpx;
      color: $text-color-primary;

      &.placeholder {
        color: $text-color-quaternary;
      }
    }
  }
}

/* 文本域 */
.textarea {
  width: 100%;
  min-height: 200rpx;
  padding: $spacing-md;
  background-color: $bg-color-light;
  border: 1px solid $border-color-light;
  border-radius: $border-radius-base;
  font-size: 28rpx;
  color: $text-color-primary;
  line-height: 1.5;

  &::placeholder {
    color: $text-color-quaternary;
  }
}

.char-count {
  text-align: right;
  font-size: 24rpx;
  color: $text-color-quaternary;
  margin-top: $spacing-xs;
}

/* 标签列表 */
.tag-list {
  display: flex;
  flex-wrap: wrap;
  gap: $spacing-md;

  .tag-item {
    padding: $spacing-sm $spacing-md;
    background-color: $bg-color-light;
    color: $text-color-secondary;
    font-size: 26rpx;
    border-radius: $border-radius-base;
    border: 1px solid transparent;

    &.active {
      background-color: rgba($primary-color, 0.1);
      color: $primary-color;
      border-color: $primary-color;
    }
  }
}

/* 提交区域 */
.submit-section {
  padding: $spacing-lg;

  .submit-btn {
    width: 100%;
    height: 88rpx;
    background-color: $primary-color;
    color: #fff;
    font-size: 32rpx;
    border: none;
    border-radius: $border-radius-base;

    &:disabled {
      background-color: $text-color-quaternary;
    }
  }
}
</style>

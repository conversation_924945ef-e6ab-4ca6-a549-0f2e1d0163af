// 数据库初始化脚本
// 在 HBuilderX 中运行此脚本来初始化数据库

const db = uniCloud.database();

// 初始化数据库集合和索引
async function initDatabase() {
  console.log('开始初始化数据库...');
  
  try {
    // 1. 创建 house 集合索引
    console.log('创建 house 集合索引...');
    await db.collection('house').createIndex({
      name: 'status_index',
      fields: [{ name: 'status', direction: 1 }]
    });
    
    await db.collection('house').createIndex({
      name: 'owner_index',
      fields: [{ name: 'owner_id', direction: 1 }]
    });
    
    await db.collection('house').createIndex({
      name: 'location_index',
      fields: [
        { name: 'location.city', direction: 1 },
        { name: 'location.district', direction: 1 }
      ]
    });
    
    await db.collection('house').createIndex({
      name: 'price_index',
      fields: [{ name: 'price', direction: 1 }]
    });
    
    await db.collection('house').createIndex({
      name: 'created_time_index',
      fields: [{ name: 'created_at', direction: -1 }]
    });
    
    // 2. 创建 uni-id-users 集合索引
    console.log('创建 uni-id-users 集合索引...');
    await db.collection('uni-id-users').createIndex({
      name: 'mobile_index',
      fields: [{ name: 'mobile', direction: 1 }]
    });
    
    await db.collection('uni-id-users').createIndex({
      name: 'openid_index',
      fields: [{ name: 'wx_openid.mp-weixin', direction: 1 }]
    });
    
    // 3. 创建 admin 集合索引
    console.log('创建 admin 集合索引...');
    await db.collection('admin').createIndex({
      name: 'username_index',
      fields: [{ name: 'username', direction: 1 }],
      unique: true
    });
    
    await db.collection('admin').createIndex({
      name: 'token_index',
      fields: [{ name: 'token', direction: 1 }]
    });
    
    // 4. 创建 system_config 集合索引
    console.log('创建 system_config 集合索引...');
    await db.collection('system_config').createIndex({
      name: 'key_index',
      fields: [{ name: 'key', direction: 1 }],
      unique: true
    });
    
    console.log('数据库索引创建完成！');
    
    // 5. 初始化管理员账号
    console.log('初始化管理员账号...');
    const crypto = require('crypto');
    const adminPassword = crypto.createHash('md5').update('admin123').digest('hex');
    
    const adminData = {
      username: 'admin',
      password: adminPassword,
      role: 'super_admin',
      created_at: new Date(),
      last_login: null,
      token: null
    };
    
    // 检查是否已存在管理员账号
    const existAdmin = await db.collection('admin').where({ username: 'admin' }).get();
    if (existAdmin.data.length === 0) {
      await db.collection('admin').add(adminData);
      console.log('管理员账号创建成功！用户名: admin, 密码: admin123');
    } else {
      console.log('管理员账号已存在，跳过创建');
    }
    
    // 6. 初始化系统配置
    console.log('初始化系统配置...');
    const systemConfigs = [
      {
        key: 'site_name',
        value: '房屋租赁平台',
        desc: '网站名称'
      },
      {
        key: 'banner_images',
        value: JSON.stringify([]),
        desc: '首页轮播图'
      },
      {
        key: 'contact_phone',
        value: '************',
        desc: '客服电话'
      },
      {
        key: 'announcement',
        value: '欢迎使用房屋租赁平台！',
        desc: '首页公告'
      },
      {
        key: 'max_publish_count',
        value: '10',
        desc: '用户最大发布房源数量'
      }
    ];
    
    for (const config of systemConfigs) {
      const existConfig = await db.collection('system_config').where({ key: config.key }).get();
      if (existConfig.data.length === 0) {
        await db.collection('system_config').add({
          ...config,
          updated_at: new Date()
        });
      }
    }
    
    console.log('系统配置初始化完成！');
    console.log('数据库初始化完成！');
    
  } catch (error) {
    console.error('数据库初始化失败：', error);
  }
}

// 执行初始化
initDatabase();

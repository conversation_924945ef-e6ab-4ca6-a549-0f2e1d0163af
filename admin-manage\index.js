'use strict';

const crypto = require('crypto');

exports.main = async (event, context) => {
  console.log('admin-manage 云函数被调用，参数：', event);
  
  const { action } = event;
  
  try {
    switch (action) {
      case 'adminLogin':
        return await adminLogin(event);
      case 'getHouseList':
        return await getHouseList(event);
      case 'auditHouse':
        return await auditHouse(event);
      case 'deleteHouse':
        return await deleteHouse(event);
      case 'getUserList':
        return await getUserList(event);
      case 'banUser':
        return await banUser(event);
      case 'getStatistics':
        return await getStatistics(event);
      case 'updateSystemConfig':
        return await updateSystemConfig(event);
      case 'getSystemConfig':
        return await getSystemConfig(event);
      default:
        return {
          code: 1001,
          message: '不支持的操作类型',
          data: null
        };
    }
  } catch (error) {
    console.error('admin-manage 云函数执行错误：', error);
    return {
      code: 1005,
      message: error.message || '操作失败',
      data: null
    };
  }
};

// 管理员登录
async function adminLogin(event) {
  const { username, password } = event;
  
  if (!username || !password) {
    return {
      code: 1001,
      message: '用户名和密码不能为空',
      data: null
    };
  }
  
  try {
    const db = uniCloud.database();
    const adminCollection = db.collection('admin');
    
    // 密码加密
    const hashedPassword = crypto.createHash('md5').update(password).digest('hex');
    
    const result = await adminCollection
      .where({
        username,
        password: hashedPassword
      })
      .get();
    
    if (result.data.length === 0) {
      return {
        code: 5001,
        message: '用户名或密码错误',
        data: null
      };
    }
    
    const adminInfo = result.data[0];
    
    // 生成token（简单实现，实际项目建议使用JWT）
    const token = crypto.randomBytes(32).toString('hex');
    
    // 更新最后登录时间
    await adminCollection.doc(adminInfo._id).update({
      last_login: new Date(),
      token: token
    });
    
    return {
      code: 0,
      message: '登录成功',
      data: {
        token,
        adminInfo: {
          _id: adminInfo._id,
          username: adminInfo.username,
          role: adminInfo.role
        }
      }
    };
    
  } catch (error) {
    console.error('管理员登录错误：', error);
    return {
      code: 5001,
      message: '登录失败',
      data: null
    };
  }
}

// 验证管理员权限
async function verifyAdmin(token) {
  if (!token) {
    return { valid: false, message: '缺少token' };
  }
  
  try {
    const db = uniCloud.database();
    const adminCollection = db.collection('admin');
    
    const result = await adminCollection
      .where({ token })
      .get();
    
    if (result.data.length === 0) {
      return { valid: false, message: '无效的token' };
    }
    
    return { 
      valid: true, 
      adminInfo: result.data[0] 
    };
    
  } catch (error) {
    return { valid: false, message: '验证失败' };
  }
}

// 获取房源列表（管理员）
async function getHouseList(event) {
  const { page = 1, limit = 10, status = 'all' } = event;
  
  // 验证管理员权限
  const authResult = await verifyAdmin(event.adminToken);
  if (!authResult.valid) {
    return {
      code: 1002,
      message: authResult.message,
      data: null
    };
  }
  
  try {
    const db = uniCloud.database();
    const houseCollection = db.collection('house');
    
    // 构建查询条件
    let whereCondition = {};
    if (status !== 'all') {
      whereCondition.status = status;
    }
    
    // 查询总数
    const countResult = await houseCollection.where(whereCondition).count();
    
    // 查询列表数据
    const listResult = await houseCollection
      .where(whereCondition)
      .orderBy('created_at', 'desc')
      .skip((page - 1) * limit)
      .limit(limit)
      .get();
    
    return {
      code: 0,
      message: '获取成功',
      data: {
        list: listResult.data,
        total: countResult.total,
        page: Number(page),
        limit: Number(limit)
      }
    };
    
  } catch (error) {
    console.error('获取房源列表错误：', error);
    return {
      code: 1005,
      message: '获取房源列表失败',
      data: null
    };
  }
}

// 审核房源
async function auditHouse(event) {
  const { house_id, status, reject_reason = '' } = event;
  
  if (!house_id || !status) {
    return {
      code: 1001,
      message: '缺少必要参数',
      data: null
    };
  }
  
  if (!['approved', 'rejected'].includes(status)) {
    return {
      code: 1001,
      message: '无效的审核状态',
      data: null
    };
  }
  
  // 验证管理员权限
  const authResult = await verifyAdmin(event.adminToken);
  if (!authResult.valid) {
    return {
      code: 1002,
      message: authResult.message,
      data: null
    };
  }
  
  try {
    const db = uniCloud.database();
    const houseCollection = db.collection('house');
    
    const updateData = {
      status,
      updated_at: new Date()
    };
    
    if (status === 'rejected' && reject_reason) {
      updateData.reject_reason = reject_reason;
    }
    
    await houseCollection.doc(house_id).update(updateData);
    
    return {
      code: 0,
      message: '审核成功',
      data: null
    };
    
  } catch (error) {
    console.error('审核房源错误：', error);
    return {
      code: 1005,
      message: '审核房源失败',
      data: null
    };
  }
}

// 删除房源（管理员）
async function deleteHouse(event) {
  const { house_id } = event;
  
  if (!house_id) {
    return {
      code: 1001,
      message: '缺少房源ID',
      data: null
    };
  }
  
  // 验证管理员权限
  const authResult = await verifyAdmin(event.adminToken);
  if (!authResult.valid) {
    return {
      code: 1002,
      message: authResult.message,
      data: null
    };
  }
  
  try {
    const db = uniCloud.database();
    const houseCollection = db.collection('house');
    
    await houseCollection.doc(house_id).remove();
    
    return {
      code: 0,
      message: '删除成功',
      data: null
    };

  } catch (error) {
    console.error('删除房源错误：', error);
    return {
      code: 1005,
      message: '删除房源失败',
      data: null
    };
  }
}

// 获取用户列表
async function getUserList(event) {
  const { page = 1, limit = 10, keyword = '' } = event;

  // 验证管理员权限
  const authResult = await verifyAdmin(event.adminToken);
  if (!authResult.valid) {
    return {
      code: 1002,
      message: authResult.message,
      data: null
    };
  }

  try {
    const db = uniCloud.database();
    const userCollection = db.collection('uni-id-users');

    // 构建查询条件
    let whereCondition = {};
    if (keyword) {
      whereCondition[db.command.or] = [
        { nickname: new RegExp(keyword, 'i') },
        { mobile: new RegExp(keyword, 'i') }
      ];
    }

    // 查询总数
    const countResult = await userCollection.where(whereCondition).count();

    // 查询列表数据
    const listResult = await userCollection
      .where(whereCondition)
      .field({
        password: false,
        token: false
      })
      .orderBy('register_date', 'desc')
      .skip((page - 1) * limit)
      .limit(limit)
      .get();

    return {
      code: 0,
      message: '获取成功',
      data: {
        list: listResult.data,
        total: countResult.total,
        page: Number(page),
        limit: Number(limit)
      }
    };

  } catch (error) {
    console.error('获取用户列表错误：', error);
    return {
      code: 1005,
      message: '获取用户列表失败',
      data: null
    };
  }
}

// 封禁/解封用户
async function banUser(event) {
  const { user_id, is_banned } = event;

  if (!user_id || typeof is_banned !== 'boolean') {
    return {
      code: 1001,
      message: '缺少必要参数',
      data: null
    };
  }

  // 验证管理员权限
  const authResult = await verifyAdmin(event.adminToken);
  if (!authResult.valid) {
    return {
      code: 1002,
      message: authResult.message,
      data: null
    };
  }

  try {
    const db = uniCloud.database();
    const userCollection = db.collection('uni-id-users');

    await userCollection.doc(user_id).update({
      is_banned,
      updated_at: new Date()
    });

    return {
      code: 0,
      message: is_banned ? '封禁成功' : '解封成功',
      data: null
    };

  } catch (error) {
    console.error('用户封禁操作错误：', error);
    return {
      code: 1005,
      message: '操作失败',
      data: null
    };
  }
}

// 获取数据统计
async function getStatistics(event) {
  // 验证管理员权限
  const authResult = await verifyAdmin(event.adminToken);
  if (!authResult.valid) {
    return {
      code: 1002,
      message: authResult.message,
      data: null
    };
  }

  try {
    const db = uniCloud.database();
    const houseCollection = db.collection('house');
    const userCollection = db.collection('uni-id-users');

    // 统计房源数据
    const totalHousesResult = await houseCollection.count();
    const pendingHousesResult = await houseCollection.where({ status: 'pending' }).count();
    const approvedHousesResult = await houseCollection.where({ status: 'approved' }).count();
    const rejectedHousesResult = await houseCollection.where({ status: 'rejected' }).count();

    // 统计用户数据
    const totalUsersResult = await userCollection.count();
    const bannedUsersResult = await userCollection.where({ is_banned: true }).count();

    // 获取今日新增数据
    const today = new Date();
    today.setHours(0, 0, 0, 0);

    const todayHousesResult = await houseCollection
      .where({
        created_at: db.command.gte(today)
      })
      .count();

    const todayUsersResult = await userCollection
      .where({
        register_date: db.command.gte(today)
      })
      .count();

    return {
      code: 0,
      message: '获取成功',
      data: {
        houses: {
          total: totalHousesResult.total,
          pending: pendingHousesResult.total,
          approved: approvedHousesResult.total,
          rejected: rejectedHousesResult.total,
          today: todayHousesResult.total
        },
        users: {
          total: totalUsersResult.total,
          banned: bannedUsersResult.total,
          today: todayUsersResult.total
        }
      }
    };

  } catch (error) {
    console.error('获取统计数据错误：', error);
    return {
      code: 1005,
      message: '获取统计数据失败',
      data: null
    };
  }
}

// 更新系统配置
async function updateSystemConfig(event) {
  const { key, value, desc } = event;

  if (!key || value === undefined) {
    return {
      code: 1001,
      message: '缺少必要参数',
      data: null
    };
  }

  // 验证管理员权限
  const authResult = await verifyAdmin(event.adminToken);
  if (!authResult.valid) {
    return {
      code: 1002,
      message: authResult.message,
      data: null
    };
  }

  try {
    const db = uniCloud.database();
    const configCollection = db.collection('system_config');

    const configData = {
      key,
      value,
      desc: desc || '',
      updated_at: new Date()
    };

    // 尝试更新，如果不存在则创建
    const existResult = await configCollection.where({ key }).get();

    if (existResult.data.length > 0) {
      await configCollection.doc(existResult.data[0]._id).update(configData);
    } else {
      await configCollection.add(configData);
    }

    return {
      code: 0,
      message: '更新成功',
      data: null
    };

  } catch (error) {
    console.error('更新系统配置错误：', error);
    return {
      code: 1005,
      message: '更新系统配置失败',
      data: null
    };
  }
}

// 获取系统配置
async function getSystemConfig(event) {
  const { key } = event;

  try {
    const db = uniCloud.database();
    const configCollection = db.collection('system_config');

    let result;
    if (key) {
      // 获取指定配置
      result = await configCollection.where({ key }).get();
      return {
        code: 0,
        message: '获取成功',
        data: result.data.length > 0 ? result.data[0] : null
      };
    } else {
      // 获取所有配置
      result = await configCollection.get();
      return {
        code: 0,
        message: '获取成功',
        data: result.data
      };
    }

  } catch (error) {
    console.error('获取系统配置错误：', error);
    return {
      code: 1005,
      message: '获取系统配置失败',
      data: null
    };
  }
}

{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/web/project/前端10/pages/publish/add.vue?30a6", "webpack:///D:/web/project/前端10/pages/publish/add.vue?255b", "webpack:///D:/web/project/前端10/pages/publish/add.vue?63a4", "webpack:///D:/web/project/前端10/pages/publish/add.vue?4ac6", "uni-app:///pages/publish/add.vue", "webpack:///D:/web/project/前端10/pages/publish/add.vue?b2a6", "webpack:///D:/web/project/前端10/pages/publish/add.vue?631c"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "submitting", "formData", "title", "type", "price", "address", "area", "floor", "orientation", "decoration", "description", "contact_phone", "images", "tags", "typeOptions", "label", "value", "orientationOptions", "decorationOptions", "tagOptions", "computed", "selectedType", "methods", "chooseImage", "uni", "count", "sizeType", "sourceType", "success", "res", "filePath", "uploadAPI", "fileId", "console", "deleteImage", "onTypeChange", "onOrientationChange", "onDecorationChange", "chooseLocation", "toggleTag", "validateForm", "icon", "submitForm", "houseAPI", "setTimeout", "url"], "mappings": ";;;;;;;;;;;;;AAAA;AAE2D;AAC3D;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,YAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAA4H;AAC5H;AACuD;AACL;AACsC;;;AAGxF;AACyK;AACzK,gBAAgB,gLAAU;AAC1B,EAAE,yEAAM;AACR,EAAE,0FAAM;AACR,EAAE,mGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,8FAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AC9BA;AAAA;AAAA;AAAA;AAA8nB,CAAgB,4nBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;ACoMlpB;AAAA;AAAA;AAAA;AAAA,eAEA;EACAC;IACA;MACAC;MACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;MACA;MAEAC,cACA;QAAAC;QAAAC;MAAA,GACA;QAAAD;QAAAC;MAAA,GACA;QAAAD;QAAAC;MAAA,EACA;MAEAC,qBACA,gCACA,uBACA;MAEAC,oBACA,uBACA;MAEAC,aACA,8BACA,6BACA;IAEA;EACA;EAEAC;IACAC;MAAA;MACA;QAAA;MAAA;MACA;IACA;EACA;EAEAC;IACA;IACAC;MAAA;MACA;MACAC;QACAC;QACAC;QACAC;QACAC;UAAA;YAAA;YAAA;cAAA;gBAAA;kBAAA;oBACAJ;sBACAtB;oBACA;oBAAA;oBAAA,uCAGA2B;oBAAA;oBAAA;kBAAA;oBAAA;sBAAA;sBAAA;oBAAA;oBAAAC;oBAAA;oBAAA,OACAC;kBAAA;oBAAAC;oBACA;kBAAA;oBAAA;oBAAA;kBAAA;oBAAA;oBAAA;kBAAA;oBAAA;oBAAA;oBAAA;kBAAA;oBAAA;oBAAA;oBAAA;kBAAA;oBAAA;oBAAA;kBAAA;oBAAA;oBAAA;oBAGAC;kBAAA;oBAAA;oBAEAT;oBAAA;kBAAA;kBAAA;oBAAA;gBAAA;cAAA;YAAA;UAAA,CAEA;UAAA;YAAA;UAAA;UAAA;QAAA;MACA;IACA;IAEA;IACAU;MACA;IACA;IAEA;IACAC;MACA;MACA;IACA;IAEA;IACAC;MACA;MACA;IACA;IAEA;IACAC;MACA;MACA;IACA;IAEA;IACAC;MAAA;MACAd;QACAI;UACA;UACA;UACA;QACA;MACA;IACA;IAEA;IACAW;MACA;MACA;QACA;MACA;QACA;MACA;IACA;IAEA;IACAC;MACA;QACAhB;UACAtB;UACAuC;QACA;QACA;MACA;MAEA;QACAjB;UACAtB;UACAuC;QACA;QACA;MACA;MAEA;QACAjB;UACAtB;UACAuC;QACA;QACA;MACA;MAEA;QACAjB;UACAtB;UACAuC;QACA;QACA;MACA;MAEA;QACAjB;UACAtB;UACAuC;QACA;QACA;MACA;MAEA;IACA;IAEA;IACAC;MAAA;MAAA;QAAA;UAAA;YAAA;cAAA;gBAAA,IACA;kBAAA;kBAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAKA;gBAAA;gBAAA,OAEAC;cAAA;gBAEAnB;kBACAtB;kBACAuC;gBACA;;gBAEA;gBACAG;kBACApB;oBACAqB;kBACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAGAZ;cAAA;gBAAA;gBAEA;gBAAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACzYA;AAAA;AAAA;AAAA;AAAytC,CAAgB,+oCAAG,EAAC,C;;;;;;;;;;;ACA7uC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/publish/add.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;import '@dcloudio/vue-cli-plugin-uni/packages/uni-cloud/dist/index.js';\nimport Vue from 'vue'\nimport Page from './pages/publish/add.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./add.vue?vue&type=template&id=333d9b38&scoped=true&\"\nvar renderjs\nimport script from \"./add.vue?vue&type=script&lang=js&\"\nexport * from \"./add.vue?vue&type=script&lang=js&\"\nimport style0 from \"./add.vue?vue&type=style&index=0&id=333d9b38&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"333d9b38\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/publish/add.vue\"\nexport default component.exports", "export * from \"-!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./add.vue?vue&type=template&id=333d9b38&scoped=true&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 = _vm.formData.images.length\n  var g1 = g0 < 9 ? _vm.formData.images.length : null\n  var g2 = _vm.formData.description.length\n  var l0 = _vm.__map(_vm.tagOptions, function (tag, __i0__) {\n    var $orig = _vm.__get_orig(tag)\n    var g3 = _vm.formData.tags.includes(tag)\n    return {\n      $orig: $orig,\n      g3: g3,\n    }\n  })\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n        g1: g1,\n        g2: g2,\n        l0: l0,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./add.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./add.vue?vue&type=script&lang=js&\"", "<template>\n  <view class=\"publish-page\">\n    <form @submit=\"submitForm\">\n      <!-- 房源图片 -->\n      <view class=\"form-section\">\n        <view class=\"section-title\">房源图片 <text class=\"required\">*</text></view>\n        <view class=\"image-upload\">\n          <view class=\"image-list\">\n            <view \n              class=\"image-item\" \n              v-for=\"(image, index) in formData.images\" \n              :key=\"index\"\n            >\n              <image class=\"uploaded-image\" :src=\"image\" mode=\"aspectFill\"></image>\n              <view class=\"delete-btn\" @click=\"deleteImage(index)\">\n                <uni-icons type=\"close\" size=\"16\" color=\"#fff\"></uni-icons>\n              </view>\n            </view>\n            <view \n              class=\"upload-btn\" \n              @click=\"chooseImage\"\n              v-if=\"formData.images.length < 9\"\n            >\n              <uni-icons type=\"camera\" size=\"32\" color=\"#999\"></uni-icons>\n              <text class=\"upload-text\">添加图片</text>\n              <text class=\"upload-count\">{{ formData.images.length }}/9</text>\n            </view>\n          </view>\n        </view>\n      </view>\n\n      <!-- 基本信息 -->\n      <view class=\"form-section\">\n        <view class=\"section-title\">基本信息</view>\n        \n        <view class=\"form-item\">\n          <text class=\"label\">房源标题 <text class=\"required\">*</text></text>\n          <input \n            class=\"input\" \n            v-model=\"formData.title\" \n            placeholder=\"请输入房源标题\"\n            maxlength=\"50\"\n          />\n        </view>\n        \n        <view class=\"form-item\">\n          <text class=\"label\">房源类型 <text class=\"required\">*</text></text>\n          <picker \n            mode=\"selector\" \n            :range=\"typeOptions\" \n            range-key=\"label\"\n            @change=\"onTypeChange\"\n          >\n            <view class=\"picker\">\n              {{ selectedType || '请选择房源类型' }}\n              <uni-icons type=\"right\" size=\"14\" color=\"#999\"></uni-icons>\n            </view>\n          </picker>\n        </view>\n        \n        <view class=\"form-item\">\n          <text class=\"label\">租金 <text class=\"required\">*</text></text>\n          <view class=\"price-input\">\n            <input \n              class=\"input\" \n              v-model=\"formData.price\" \n              placeholder=\"请输入租金\"\n              type=\"number\"\n            />\n            <text class=\"unit\">元/月</text>\n          </view>\n        </view>\n        \n        <view class=\"form-item\">\n          <text class=\"label\">房源地址 <text class=\"required\">*</text></text>\n          <view class=\"address-input\" @click=\"chooseLocation\">\n            <text class=\"address-text\" :class=\"{ placeholder: !formData.address }\">\n              {{ formData.address || '请选择房源地址' }}\n            </text>\n            <uni-icons type=\"right\" size=\"14\" color=\"#999\"></uni-icons>\n          </view>\n        </view>\n      </view>\n\n      <!-- 房源配置 -->\n      <view class=\"form-section\">\n        <view class=\"section-title\">房源配置</view>\n        \n        <view class=\"form-item\">\n          <text class=\"label\">面积</text>\n          <view class=\"area-input\">\n            <input \n              class=\"input\" \n              v-model=\"formData.area\" \n              placeholder=\"请输入面积\"\n              type=\"number\"\n            />\n            <text class=\"unit\">㎡</text>\n          </view>\n        </view>\n        \n        <view class=\"form-item\">\n          <text class=\"label\">楼层</text>\n          <input \n            class=\"input\" \n            v-model=\"formData.floor\" \n            placeholder=\"如：5/10层\"\n          />\n        </view>\n        \n        <view class=\"form-item\">\n          <text class=\"label\">朝向</text>\n          <picker \n            mode=\"selector\" \n            :range=\"orientationOptions\"\n            @change=\"onOrientationChange\"\n          >\n            <view class=\"picker\">\n              {{ formData.orientation || '请选择朝向' }}\n              <uni-icons type=\"right\" size=\"14\" color=\"#999\"></uni-icons>\n            </view>\n          </picker>\n        </view>\n        \n        <view class=\"form-item\">\n          <text class=\"label\">装修</text>\n          <picker \n            mode=\"selector\" \n            :range=\"decorationOptions\"\n            @change=\"onDecorationChange\"\n          >\n            <view class=\"picker\">\n              {{ formData.decoration || '请选择装修情况' }}\n              <uni-icons type=\"right\" size=\"14\" color=\"#999\"></uni-icons>\n            </view>\n          </picker>\n        </view>\n      </view>\n\n      <!-- 房源描述 -->\n      <view class=\"form-section\">\n        <view class=\"section-title\">房源描述</view>\n        <textarea \n          class=\"textarea\" \n          v-model=\"formData.description\" \n          placeholder=\"请详细描述房源情况，如周边配套、交通等\"\n          maxlength=\"500\"\n        ></textarea>\n        <view class=\"char-count\">{{ formData.description.length }}/500</view>\n      </view>\n\n      <!-- 房源标签 -->\n      <view class=\"form-section\">\n        <view class=\"section-title\">房源标签</view>\n        <view class=\"tag-list\">\n          <view \n            class=\"tag-item\" \n            v-for=\"tag in tagOptions\" \n            :key=\"tag\"\n            :class=\"{ active: formData.tags.includes(tag) }\"\n            @click=\"toggleTag(tag)\"\n          >\n            {{ tag }}\n          </view>\n        </view>\n      </view>\n\n      <!-- 联系方式 -->\n      <view class=\"form-section\">\n        <view class=\"section-title\">联系方式</view>\n        <view class=\"form-item\">\n          <text class=\"label\">联系电话</text>\n          <input \n            class=\"input\" \n            v-model=\"formData.contact_phone\" \n            placeholder=\"请输入联系电话\"\n            type=\"number\"\n          />\n        </view>\n      </view>\n\n      <!-- 提交按钮 -->\n      <view class=\"submit-section\">\n        <button \n          class=\"submit-btn\" \n          :disabled=\"submitting\"\n          @click=\"submitForm\"\n        >\n          {{ submitting ? '发布中...' : '发布房源' }}\n        </button>\n      </view>\n    </form>\n  </view>\n</template>\n\n<script>\nimport { houseAPI, uploadAPI } from '@/utils/request.js'\n\nexport default {\n  data() {\n    return {\n      submitting: false,\n      formData: {\n        title: '',\n        type: '',\n        price: '',\n        address: '',\n        area: '',\n        floor: '',\n        orientation: '',\n        decoration: '',\n        description: '',\n        contact_phone: '',\n        images: [],\n        tags: []\n      },\n      \n      typeOptions: [\n        { label: '整租', value: 'whole' },\n        { label: '合租', value: 'shared' },\n        { label: '短租', value: 'short' }\n      ],\n      \n      orientationOptions: [\n        '南北', '东西', '南', '北', '东', '西', \n        '东南', '西南', '东北', '西北'\n      ],\n      \n      decorationOptions: [\n        '毛坯', '简装', '精装', '豪装'\n      ],\n      \n      tagOptions: [\n        '近地铁', '拎包入住', '独立卫浴', '有阳台', \n        '有空调', '有洗衣机', '有冰箱', '有电梯',\n        '停车位', '宽带', '可做饭', '可洗澡'\n      ]\n    }\n  },\n  \n  computed: {\n    selectedType() {\n      const type = this.typeOptions.find(item => item.value === this.formData.type)\n      return type ? type.label : ''\n    }\n  },\n  \n  methods: {\n    // 选择图片\n    chooseImage() {\n      const remainCount = 9 - this.formData.images.length\n      uni.chooseImage({\n        count: remainCount,\n        sizeType: ['compressed'],\n        sourceType: ['album', 'camera'],\n        success: async (res) => {\n          uni.showLoading({\n            title: '上传中...'\n          })\n          \n          try {\n            for (let filePath of res.tempFilePaths) {\n              const fileId = await uploadAPI.uploadImage(filePath)\n              this.formData.images.push(fileId)\n            }\n          } catch (error) {\n            console.error('图片上传失败：', error)\n          } finally {\n            uni.hideLoading()\n          }\n        }\n      })\n    },\n    \n    // 删除图片\n    deleteImage(index) {\n      this.formData.images.splice(index, 1)\n    },\n    \n    // 选择房源类型\n    onTypeChange(e) {\n      const index = e.detail.value\n      this.formData.type = this.typeOptions[index].value\n    },\n    \n    // 选择朝向\n    onOrientationChange(e) {\n      const index = e.detail.value\n      this.formData.orientation = this.orientationOptions[index]\n    },\n    \n    // 选择装修\n    onDecorationChange(e) {\n      const index = e.detail.value\n      this.formData.decoration = this.decorationOptions[index]\n    },\n    \n    // 选择位置\n    chooseLocation() {\n      uni.chooseLocation({\n        success: (res) => {\n          this.formData.address = res.address\n          this.formData.latitude = res.latitude\n          this.formData.longitude = res.longitude\n        }\n      })\n    },\n    \n    // 切换标签\n    toggleTag(tag) {\n      const index = this.formData.tags.indexOf(tag)\n      if (index > -1) {\n        this.formData.tags.splice(index, 1)\n      } else {\n        this.formData.tags.push(tag)\n      }\n    },\n    \n    // 表单验证\n    validateForm() {\n      if (!this.formData.title.trim()) {\n        uni.showToast({\n          title: '请输入房源标题',\n          icon: 'none'\n        })\n        return false\n      }\n      \n      if (!this.formData.type) {\n        uni.showToast({\n          title: '请选择房源类型',\n          icon: 'none'\n        })\n        return false\n      }\n      \n      if (!this.formData.price) {\n        uni.showToast({\n          title: '请输入租金',\n          icon: 'none'\n        })\n        return false\n      }\n      \n      if (!this.formData.address) {\n        uni.showToast({\n          title: '请选择房源地址',\n          icon: 'none'\n        })\n        return false\n      }\n      \n      if (this.formData.images.length === 0) {\n        uni.showToast({\n          title: '请至少上传一张图片',\n          icon: 'none'\n        })\n        return false\n      }\n      \n      return true\n    },\n    \n    // 提交表单\n    async submitForm() {\n      if (!this.validateForm()) {\n        return\n      }\n      \n      try {\n        this.submitting = true\n        \n        await houseAPI.publishHouse(this.formData)\n        \n        uni.showToast({\n          title: '发布成功',\n          icon: 'success'\n        })\n        \n        // 跳转到我的发布页面\n        setTimeout(() => {\n          uni.redirectTo({\n            url: '/pages/publish/my'\n          })\n        }, 1500)\n        \n      } catch (error) {\n        console.error('发布房源失败：', error)\n      } finally {\n        this.submitting = false\n      }\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.publish-page {\n  background-color: $bg-color-page;\n  padding-bottom: $spacing-xl;\n}\n\n/* 表单区块 */\n.form-section {\n  background-color: #fff;\n  margin-bottom: $spacing-md;\n  padding: $spacing-lg;\n\n  .section-title {\n    font-size: 32rpx;\n    font-weight: 600;\n    color: $text-color-primary;\n    margin-bottom: $spacing-lg;\n\n    .required {\n      color: $error-color;\n    }\n  }\n}\n\n/* 图片上传 */\n.image-upload {\n  .image-list {\n    display: flex;\n    flex-wrap: wrap;\n    gap: $spacing-md;\n\n    .image-item {\n      position: relative;\n      width: 200rpx;\n      height: 200rpx;\n      border-radius: $border-radius-base;\n      overflow: hidden;\n\n      .uploaded-image {\n        width: 100%;\n        height: 100%;\n      }\n\n      .delete-btn {\n        position: absolute;\n        top: 8rpx;\n        right: 8rpx;\n        width: 40rpx;\n        height: 40rpx;\n        background-color: rgba(0, 0, 0, 0.5);\n        border-radius: 50%;\n        display: flex;\n        align-items: center;\n        justify-content: center;\n      }\n    }\n\n    .upload-btn {\n      width: 200rpx;\n      height: 200rpx;\n      border: 2rpx dashed $border-color-base;\n      border-radius: $border-radius-base;\n      display: flex;\n      flex-direction: column;\n      align-items: center;\n      justify-content: center;\n      background-color: $bg-color-light;\n\n      .upload-text {\n        font-size: 24rpx;\n        color: $text-color-tertiary;\n        margin-top: $spacing-xs;\n      }\n\n      .upload-count {\n        font-size: 20rpx;\n        color: $text-color-quaternary;\n        margin-top: 4rpx;\n      }\n    }\n  }\n}\n\n/* 表单项 */\n.form-item {\n  margin-bottom: $spacing-lg;\n\n  &:last-child {\n    margin-bottom: 0;\n  }\n\n  .label {\n    display: block;\n    font-size: 28rpx;\n    color: $text-color-primary;\n    margin-bottom: $spacing-sm;\n\n    .required {\n      color: $error-color;\n    }\n  }\n\n  .input {\n    width: 100%;\n    height: 80rpx;\n    padding: 0 $spacing-md;\n    background-color: $bg-color-light;\n    border: 1px solid $border-color-light;\n    border-radius: $border-radius-base;\n    font-size: 28rpx;\n    color: $text-color-primary;\n\n    &::placeholder {\n      color: $text-color-quaternary;\n    }\n  }\n\n  .picker {\n    display: flex;\n    align-items: center;\n    justify-content: space-between;\n    height: 80rpx;\n    padding: 0 $spacing-md;\n    background-color: $bg-color-light;\n    border: 1px solid $border-color-light;\n    border-radius: $border-radius-base;\n    font-size: 28rpx;\n    color: $text-color-primary;\n  }\n\n  .price-input,\n  .area-input {\n    display: flex;\n    align-items: center;\n\n    .input {\n      flex: 1;\n      margin-right: $spacing-sm;\n    }\n\n    .unit {\n      font-size: 28rpx;\n      color: $text-color-secondary;\n    }\n  }\n\n  .address-input {\n    display: flex;\n    align-items: center;\n    justify-content: space-between;\n    height: 80rpx;\n    padding: 0 $spacing-md;\n    background-color: $bg-color-light;\n    border: 1px solid $border-color-light;\n    border-radius: $border-radius-base;\n\n    .address-text {\n      font-size: 28rpx;\n      color: $text-color-primary;\n\n      &.placeholder {\n        color: $text-color-quaternary;\n      }\n    }\n  }\n}\n\n/* 文本域 */\n.textarea {\n  width: 100%;\n  min-height: 200rpx;\n  padding: $spacing-md;\n  background-color: $bg-color-light;\n  border: 1px solid $border-color-light;\n  border-radius: $border-radius-base;\n  font-size: 28rpx;\n  color: $text-color-primary;\n  line-height: 1.5;\n\n  &::placeholder {\n    color: $text-color-quaternary;\n  }\n}\n\n.char-count {\n  text-align: right;\n  font-size: 24rpx;\n  color: $text-color-quaternary;\n  margin-top: $spacing-xs;\n}\n\n/* 标签列表 */\n.tag-list {\n  display: flex;\n  flex-wrap: wrap;\n  gap: $spacing-md;\n\n  .tag-item {\n    padding: $spacing-sm $spacing-md;\n    background-color: $bg-color-light;\n    color: $text-color-secondary;\n    font-size: 26rpx;\n    border-radius: $border-radius-base;\n    border: 1px solid transparent;\n\n    &.active {\n      background-color: rgba($primary-color, 0.1);\n      color: $primary-color;\n      border-color: $primary-color;\n    }\n  }\n}\n\n/* 提交区域 */\n.submit-section {\n  padding: $spacing-lg;\n\n  .submit-btn {\n    width: 100%;\n    height: 88rpx;\n    background-color: $primary-color;\n    color: #fff;\n    font-size: 32rpx;\n    border: none;\n    border-radius: $border-radius-base;\n\n    &:disabled {\n      background-color: $text-color-quaternary;\n    }\n  }\n}\n</style>\n", "import mod from \"-!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./add.vue?vue&type=style&index=0&id=333d9b38&lang=scss&scoped=true&\"; export default mod; export * from \"-!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./add.vue?vue&type=style&index=0&id=333d9b38&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754033370477\n      var cssReload = require(\"D:/web/wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}
@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 自定义项目样式变量 */
/* 主题色 */
/* 辅助色 */
/* 中性色 */
/* 背景色 */
/* 边框色 */
/* 阴影 */
/* 圆角 */
/* 间距 */
.profile-page.data-v-14bc1b43 {
  background-color: #F5F5F5;
  min-height: 100vh;
}
/* 用户信息 */
.user-section.data-v-14bc1b43 {
  background-color: #fff;
  margin-bottom: 24rpx;
}
.user-section .user-info.data-v-14bc1b43 {
  display: flex;
  align-items: center;
  padding: 48rpx 32rpx;
}
.user-section .user-info .avatar.data-v-14bc1b43 {
  width: 120rpx;
  height: 120rpx;
  border-radius: 50%;
  margin-right: 32rpx;
}
.user-section .user-info .user-details.data-v-14bc1b43 {
  flex: 1;
}
.user-section .user-info .user-details .nickname.data-v-14bc1b43 {
  display: block;
  font-size: 36rpx;
  font-weight: 600;
  color: #262626;
  margin-bottom: 8rpx;
}
.user-section .user-info .user-details .desc.data-v-14bc1b43 {
  font-size: 26rpx;
  color: #8C8C8C;
}
.user-section .user-info .edit-btn.data-v-14bc1b43 {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #FAFAFA;
  border-radius: 50%;
}
/* 数据统计 */
.stats-section.data-v-14bc1b43 {
  background-color: #fff;
  margin-bottom: 24rpx;
  padding: 32rpx;
}
.stats-section .stats-grid.data-v-14bc1b43 {
  display: flex;
}
.stats-section .stats-grid .stats-item.data-v-14bc1b43 {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
}
.stats-section .stats-grid .stats-item .stats-num.data-v-14bc1b43 {
  font-size: 48rpx;
  font-weight: 600;
  color: #007AFF;
  margin-bottom: 8rpx;
}
.stats-section .stats-grid .stats-item .stats-label.data-v-14bc1b43 {
  font-size: 26rpx;
  color: #595959;
}
/* 功能菜单 */
.menu-section .menu-group.data-v-14bc1b43 {
  background-color: #fff;
  margin-bottom: 24rpx;
}
.menu-section .menu-group .menu-item.data-v-14bc1b43 {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 32rpx;
  border-bottom: 1px solid #F0F0F0;
}
.menu-section .menu-group .menu-item.data-v-14bc1b43:last-child {
  border-bottom: none;
}
.menu-section .menu-group .menu-item .menu-left.data-v-14bc1b43 {
  display: flex;
  align-items: center;
}
.menu-section .menu-group .menu-item .menu-left .menu-text.data-v-14bc1b43 {
  margin-left: 24rpx;
  font-size: 30rpx;
  color: #262626;
}
/* 退出登录 */
.logout-section.data-v-14bc1b43 {
  padding: 32rpx;
}
.logout-section .logout-btn.data-v-14bc1b43 {
  width: 100%;
  height: 88rpx;
  background-color: #fff;
  color: #FF4D4F;
  font-size: 30rpx;
  border: 1px solid #D9D9D9;
  border-radius: 8rpx;
}

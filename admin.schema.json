{"bsonType": "object", "required": ["username", "password", "role"], "properties": {"_id": {"description": "管理员ID"}, "username": {"bsonType": "string", "title": "用户名", "description": "管理员登录用户名", "minLength": 3, "maxLength": 20}, "password": {"bsonType": "string", "title": "密码", "description": "管理员登录密码（MD5加密）"}, "role": {"bsonType": "string", "title": "角色", "description": "管理员角色", "enum": ["super_admin", "admin"], "default": "admin"}, "created_at": {"bsonType": "date", "title": "创建时间"}, "last_login": {"bsonType": "date", "title": "最后登录时间"}, "token": {"bsonType": "string", "title": "登录令牌"}}, "permission": {"read": false, "create": false, "update": false, "delete": false}}
<template>
  <view class="home-page">
    <!-- 搜索栏 -->
    <view class="search-bar">
      <view class="search-input" @click="goToSearch">
        <uni-icons type="search" size="18" color="#999"></uni-icons>
        <text class="search-placeholder">搜索房源、地址、小区</text>
      </view>
      <view class="location-btn" @click="chooseLocation">
        <uni-icons type="location" size="16" color="#007AFF"></uni-icons>
        <text class="location-text">{{ currentCity }}</text>
      </view>
    </view>

    <!-- 轮播图 -->
    <view class="banner-section">
      <swiper class="banner-swiper" indicator-dots circular autoplay>
        <swiper-item v-for="(banner, index) in bannerList" :key="index">
          <image class="banner-image" :src="banner.image" mode="aspectFill"></image>
        </swiper-item>
      </swiper>
    </view>

    <!-- 分类导航 -->
    <view class="category-section">
      <view class="category-grid">
        <view
          class="category-item"
          v-for="(category, index) in categoryList"
          :key="index"
          @click="goToCategory(category)"
        >
          <view class="category-icon">
            <uni-icons :type="category.icon" size="24" :color="category.color"></uni-icons>
          </view>
          <text class="category-text">{{ category.name }}</text>
        </view>
      </view>
    </view>

    <!-- 推荐房源 -->
    <view class="recommend-section">
      <view class="section-header">
        <text class="section-title">推荐房源</text>
        <view class="more-btn" @click="goToHouseList">
          <text class="more-text">更多</text>
          <uni-icons type="right" size="14" color="#999"></uni-icons>
        </view>
      </view>

      <view class="house-list">
        <view
          class="house-item"
          v-for="house in recommendHouses"
          :key="house._id"
          @click="goToHouseDetail(house._id)"
        >
          <image class="house-image" :src="house.images[0]" mode="aspectFill"></image>
          <view class="house-info">
            <view class="house-title">{{ house.title }}</view>
            <view class="house-desc">{{ house.description }}</view>
            <view class="house-tags">
              <text class="tag" v-for="tag in house.tags" :key="tag">{{ tag }}</text>
            </view>
            <view class="house-bottom">
              <view class="price">
                <text class="price-num">{{ house.price }}</text>
                <text class="price-unit">元/月</text>
              </view>
              <view class="location">
                <uni-icons type="location" size="12" color="#999"></uni-icons>
                <text class="location-text">{{ house.address }}</text>
              </view>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 加载更多 -->
    <view class="loading-more" v-if="loading">
      <uni-load-more :status="loadingStatus"></uni-load-more>
    </view>
  </view>
</template>

<script>
import { houseAPI } from '@/utils/request.js'

export default {
  data() {
    return {
      currentCity: '北京',
      loading: false,
      loadingStatus: 'more',
      bannerList: [
        {
          image: '/static/banner/banner1.jpg'
        },
        {
          image: '/static/banner/banner2.jpg'
        },
        {
          image: '/static/banner/banner3.jpg'
        }
      ],
      categoryList: [
        {
          name: '整租',
          icon: 'home',
          color: '#007AFF',
          type: 'whole'
        },
        {
          name: '合租',
          icon: 'person',
          color: '#52C41A',
          type: 'shared'
        },
        {
          name: '短租',
          icon: 'calendar',
          color: '#FAAD14',
          type: 'short'
        },
        {
          name: '地图找房',
          icon: 'map',
          color: '#FF4D4F',
          type: 'map'
        }
      ],
      recommendHouses: []
    }
  },

  onLoad() {
    this.loadRecommendHouses()
  },

  onPullDownRefresh() {
    this.loadRecommendHouses()
  },

  methods: {
    // 加载推荐房源
    async loadRecommendHouses() {
      try {
        this.loading = true
        this.loadingStatus = 'loading'

        const result = await houseAPI.getHouseList({
          page: 1,
          limit: 10,
          status: 'approved'
        })

        this.recommendHouses = result.data.list || []
        this.loadingStatus = 'noMore'
      } catch (error) {
        console.error('加载推荐房源失败：', error)
        this.loadingStatus = 'error'
      } finally {
        this.loading = false
        uni.stopPullDownRefresh()
      }
    },

    // 跳转到搜索页面
    goToSearch() {
      uni.navigateTo({
        url: '/pages/house/search'
      })
    },

    // 选择位置
    chooseLocation() {
      uni.chooseLocation({
        success: (res) => {
          this.currentCity = res.name || res.address
        }
      })
    },

    // 跳转到分类页面
    goToCategory(category) {
      if (category.type === 'map') {
        // 跳转到地图找房
        uni.navigateTo({
          url: '/pages/house/map'
        })
      } else {
        // 跳转到房源列表
        uni.navigateTo({
          url: `/pages/house/list?type=${category.type}`
        })
      }
    },

    // 跳转到房源列表
    goToHouseList() {
      uni.switchTab({
        url: '/pages/house/list'
      })
    },

    // 跳转到房源详情
    goToHouseDetail(houseId) {
      uni.navigateTo({
        url: `/pages/house/detail?id=${houseId}`
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.home-page {
  background-color: $bg-color-page;
  min-height: 100vh;
}

/* 搜索栏 */
.search-bar {
  display: flex;
  align-items: center;
  padding: $spacing-md;
  background-color: #fff;

  .search-input {
    flex: 1;
    display: flex;
    align-items: center;
    height: 72rpx;
    padding: 0 $spacing-md;
    background-color: $bg-color-light;
    border-radius: 36rpx;
    margin-right: $spacing-md;

    .search-placeholder {
      margin-left: $spacing-xs;
      color: $text-color-tertiary;
      font-size: 28rpx;
    }
  }

  .location-btn {
    display: flex;
    align-items: center;

    .location-text {
      margin-left: 4rpx;
      color: $primary-color;
      font-size: 26rpx;
    }
  }
}

/* 轮播图 */
.banner-section {
  margin: $spacing-md;
  border-radius: $border-radius-large;
  overflow: hidden;

  .banner-swiper {
    height: 320rpx;

    .banner-image {
      width: 100%;
      height: 100%;
    }
  }
}

/* 分类导航 */
.category-section {
  margin: $spacing-md;
  padding: $spacing-lg;
  background-color: #fff;
  border-radius: $border-radius-large;

  .category-grid {
    display: flex;
    justify-content: space-between;

    .category-item {
      display: flex;
      flex-direction: column;
      align-items: center;
      flex: 1;

      .category-icon {
        width: 88rpx;
        height: 88rpx;
        display: flex;
        align-items: center;
        justify-content: center;
        background-color: $bg-color-light;
        border-radius: $border-radius-large;
        margin-bottom: $spacing-sm;
      }

      .category-text {
        font-size: 26rpx;
        color: $text-color-secondary;
      }
    }
  }
}

/* 推荐房源 */
.recommend-section {
  margin: $spacing-md;

  .section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: $spacing-md;

    .section-title {
      font-size: 32rpx;
      font-weight: 600;
      color: $text-color-primary;
    }

    .more-btn {
      display: flex;
      align-items: center;

      .more-text {
        font-size: 26rpx;
        color: $text-color-tertiary;
        margin-right: 4rpx;
      }
    }
  }

  .house-list {
    .house-item {
      display: flex;
      background-color: #fff;
      border-radius: $border-radius-large;
      margin-bottom: $spacing-md;
      overflow: hidden;
      box-shadow: $box-shadow-light;

      .house-image {
        width: 240rpx;
        height: 180rpx;
        flex-shrink: 0;
      }

      .house-info {
        flex: 1;
        padding: $spacing-md;
        display: flex;
        flex-direction: column;
        justify-content: space-between;

        .house-title {
          font-size: 30rpx;
          font-weight: 600;
          color: $text-color-primary;
          margin-bottom: $spacing-xs;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }

        .house-desc {
          font-size: 24rpx;
          color: $text-color-tertiary;
          margin-bottom: $spacing-xs;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }

        .house-tags {
          margin-bottom: $spacing-sm;

          .tag {
            display: inline-block;
            padding: 4rpx 12rpx;
            background-color: $bg-color-light;
            color: $text-color-secondary;
            font-size: 20rpx;
            border-radius: 8rpx;
            margin-right: $spacing-xs;
          }
        }

        .house-bottom {
          display: flex;
          justify-content: space-between;
          align-items: flex-end;

          .price {
            .price-num {
              font-size: 32rpx;
              font-weight: 600;
              color: $error-color;
            }

            .price-unit {
              font-size: 24rpx;
              color: $text-color-tertiary;
            }
          }

          .location {
            display: flex;
            align-items: center;

            .location-text {
              margin-left: 4rpx;
              font-size: 22rpx;
              color: $text-color-tertiary;
              overflow: hidden;
              text-overflow: ellipsis;
              white-space: nowrap;
              max-width: 160rpx;
            }
          }
        }
      }
    }
  }
}

/* 加载更多 */
.loading-more {
  padding: $spacing-lg;
}
</style>

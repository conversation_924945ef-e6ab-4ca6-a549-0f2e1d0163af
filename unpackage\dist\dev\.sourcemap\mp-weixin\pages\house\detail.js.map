{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/web/project/前端10/pages/house/detail.vue?9e93", "webpack:///D:/web/project/前端10/pages/house/detail.vue?235f", "webpack:///D:/web/project/前端10/pages/house/detail.vue?d4df", "webpack:///D:/web/project/前端10/pages/house/detail.vue?16d5", "uni-app:///pages/house/detail.vue", "webpack:///D:/web/project/前端10/pages/house/detail.vue?3747", "webpack:///D:/web/project/前端10/pages/house/detail.vue?ecc2"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "loading", "houseId", "currentImageIndex", "houseDetail", "images", "tags", "owner", "onLoad", "onShareAppMessage", "title", "path", "imageUrl", "methods", "loadHouseDetail", "houseAPI", "result", "uni", "console", "icon", "setTimeout", "previewImage", "urls", "current", "toggleFavorite", "contactOwner", "content", "success", "url", "phoneNumber", "shareHouse", "withShareTicket"], "mappings": ";;;;;;;;;;;;;AAAA;AAE2D;AAC3D;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,eAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAA+H;AAC/H;AAC0D;AACL;AACsC;;;AAG3F;AACyK;AACzK,gBAAgB,gLAAU;AAC1B,EAAE,4EAAM;AACR,EAAE,6FAAM;AACR,EAAE,sGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,iGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACRA;AAAA;AAAA;AAAA;AAAioB,CAAgB,+nBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;ACgHrpB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eAEA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;QACAC;QACAC;QACAC;MACA;IACA;EACA;EAEAC;IACA;IACA;MACA;IACA;EACA;EAEAC;IACA;MACAC;MACAC;MACAC;IACA;EACA;EAEAC;IACA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAEA;gBAAA;gBAAA,OACAC;cAAA;gBAAAC;gBACA;;gBAEA;gBACAC;kBACAP;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAGAQ;gBACAD;kBACAP;kBACAS;gBACA;gBACA;gBACAC;kBACAH;gBACA;cAAA;gBAAA;gBAEA;gBAAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAI;MACA;MACAJ;QACAK;QACAC;MACA;IACA;IAEA;IACAC;MAAA;MAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA,KAEA;kBAAA;kBAAA;gBAAA;gBAAA;gBAAA,OACAT;cAAA;gBACA;gBACAE;kBACAP;kBACAS;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA,OAEAJ;cAAA;gBACA;gBACAE;kBACAP;kBACAS;gBACA;cAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAGAD;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAO;MAAA;MACA;MACA;MACA;QACAR;UACAP;UACAgB;UACAC;YACA;cACAV;gBACAW;cACA;YACA;UACA;QACA;QACA;MACA;;MAEA;MACA;QACAX;UACAY;QACA;MACA;QACAZ;UACAP;UACAS;QACA;MACA;IACA;IAEA;IACAW;MACA;MACAb;QACAc;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACnPA;AAAA;AAAA;AAAA;AAA4tC,CAAgB,kpCAAG,EAAC,C;;;;;;;;;;;ACAhvC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/house/detail.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;import '@dcloudio/vue-cli-plugin-uni/packages/uni-cloud/dist/index.js';\nimport Vue from 'vue'\nimport Page from './pages/house/detail.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./detail.vue?vue&type=template&id=4265f319&scoped=true&\"\nvar renderjs\nimport script from \"./detail.vue?vue&type=script&lang=js&\"\nexport * from \"./detail.vue?vue&type=script&lang=js&\"\nimport style0 from \"./detail.vue?vue&type=style&index=0&id=4265f319&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"4265f319\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/house/detail.vue\"\nexport default component.exports", "export * from \"-!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./detail.vue?vue&type=template&id=4265f319&scoped=true&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./detail.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./detail.vue?vue&type=script&lang=js&\"", "<template>\n  <view class=\"house-detail-page\">\n    <!-- 图片轮播 -->\n    <view class=\"image-section\">\n      <swiper class=\"image-swiper\" indicator-dots circular>\n        <swiper-item v-for=\"(image, index) in houseDetail.images\" :key=\"index\">\n          <image \n            class=\"house-image\" \n            :src=\"image\" \n            mode=\"aspectFill\"\n            @click=\"previewImage(index)\"\n          ></image>\n        </swiper-item>\n      </swiper>\n      <view class=\"image-count\">\n        <text>{{ currentImageIndex + 1 }}/{{ houseDetail.images?.length || 0 }}</text>\n      </view>\n    </view>\n\n    <!-- 房源信息 -->\n    <view class=\"info-section\">\n      <view class=\"price-info\">\n        <text class=\"price\">{{ houseDetail.price }}</text>\n        <text class=\"unit\">元/月</text>\n        <view class=\"favorite-btn\" @click=\"toggleFavorite\">\n          <uni-icons \n            :type=\"houseDetail.is_favorite ? 'heart-filled' : 'heart'\" \n            size=\"24\" \n            :color=\"houseDetail.is_favorite ? '#FF4D4F' : '#999'\"\n          ></uni-icons>\n        </view>\n      </view>\n      \n      <view class=\"title\">{{ houseDetail.title }}</view>\n      \n      <view class=\"tags\">\n        <text class=\"tag\" v-for=\"tag in houseDetail.tags\" :key=\"tag\">{{ tag }}</text>\n      </view>\n      \n      <view class=\"location\">\n        <uni-icons type=\"location\" size=\"16\" color=\"#999\"></uni-icons>\n        <text class=\"address\">{{ houseDetail.address }}</text>\n      </view>\n    </view>\n\n    <!-- 房源配置 -->\n    <view class=\"config-section\">\n      <view class=\"section-title\">房源配置</view>\n      <view class=\"config-grid\">\n        <view class=\"config-item\">\n          <text class=\"config-label\">面积</text>\n          <text class=\"config-value\">{{ houseDetail.area }}㎡</text>\n        </view>\n        <view class=\"config-item\">\n          <text class=\"config-label\">楼层</text>\n          <text class=\"config-value\">{{ houseDetail.floor }}</text>\n        </view>\n        <view class=\"config-item\">\n          <text class=\"config-label\">朝向</text>\n          <text class=\"config-value\">{{ houseDetail.orientation }}</text>\n        </view>\n        <view class=\"config-item\">\n          <text class=\"config-label\">装修</text>\n          <text class=\"config-value\">{{ houseDetail.decoration }}</text>\n        </view>\n      </view>\n    </view>\n\n    <!-- 房源描述 -->\n    <view class=\"desc-section\">\n      <view class=\"section-title\">房源描述</view>\n      <text class=\"desc-content\">{{ houseDetail.description }}</text>\n    </view>\n\n    <!-- 房东信息 -->\n    <view class=\"owner-section\">\n      <view class=\"section-title\">房东信息</view>\n      <view class=\"owner-info\">\n        <image class=\"owner-avatar\" :src=\"houseDetail.owner?.avatar || '/static/default-avatar.png'\"></image>\n        <view class=\"owner-details\">\n          <text class=\"owner-name\">{{ houseDetail.owner?.nickname || '房东' }}</text>\n          <text class=\"owner-desc\">{{ houseDetail.owner?.description || '暂无介绍' }}</text>\n        </view>\n        <button class=\"contact-btn\" @click=\"contactOwner\">联系房东</button>\n      </view>\n    </view>\n\n    <!-- 底部操作栏 -->\n    <view class=\"bottom-bar\">\n      <button class=\"share-btn\" @click=\"shareHouse\">\n        <uni-icons type=\"redo\" size=\"20\" color=\"#666\"></uni-icons>\n        <text>分享</text>\n      </button>\n      <button class=\"favorite-btn\" @click=\"toggleFavorite\">\n        <uni-icons \n          :type=\"houseDetail.is_favorite ? 'heart-filled' : 'heart'\" \n          size=\"20\" \n          :color=\"houseDetail.is_favorite ? '#FF4D4F' : '#666'\"\n        ></uni-icons>\n        <text>{{ houseDetail.is_favorite ? '已收藏' : '收藏' }}</text>\n      </button>\n      <button class=\"contact-btn\" @click=\"contactOwner\">立即联系</button>\n    </view>\n\n    <!-- 加载状态 -->\n    <view class=\"loading-state\" v-if=\"loading\">\n      <uni-load-more status=\"loading\"></uni-load-more>\n    </view>\n  </view>\n</template>\n\n<script>\nimport { houseAPI } from '@/utils/request.js'\n\nexport default {\n  data() {\n    return {\n      loading: true,\n      houseId: '',\n      currentImageIndex: 0,\n      houseDetail: {\n        images: [],\n        tags: [],\n        owner: {}\n      }\n    }\n  },\n  \n  onLoad(options) {\n    this.houseId = options.id\n    if (this.houseId) {\n      this.loadHouseDetail()\n    }\n  },\n  \n  onShareAppMessage() {\n    return {\n      title: this.houseDetail.title,\n      path: `/pages/house/detail?id=${this.houseId}`,\n      imageUrl: this.houseDetail.images[0]\n    }\n  },\n  \n  methods: {\n    // 加载房源详情\n    async loadHouseDetail() {\n      try {\n        this.loading = true\n        const result = await houseAPI.getHouseDetail(this.houseId)\n        this.houseDetail = result.data\n        \n        // 设置页面标题\n        uni.setNavigationBarTitle({\n          title: this.houseDetail.title\n        })\n        \n      } catch (error) {\n        console.error('加载房源详情失败：', error)\n        uni.showToast({\n          title: '加载失败',\n          icon: 'none'\n        })\n        // 返回上一页\n        setTimeout(() => {\n          uni.navigateBack()\n        }, 1500)\n      } finally {\n        this.loading = false\n      }\n    },\n    \n    // 预览图片\n    previewImage(index) {\n      this.currentImageIndex = index\n      uni.previewImage({\n        urls: this.houseDetail.images,\n        current: index\n      })\n    },\n    \n    // 切换收藏状态\n    async toggleFavorite() {\n      try {\n        if (this.houseDetail.is_favorite) {\n          await houseAPI.unfavoriteHouse(this.houseId)\n          this.houseDetail.is_favorite = false\n          uni.showToast({\n            title: '已取消收藏',\n            icon: 'none'\n          })\n        } else {\n          await houseAPI.favoriteHouse(this.houseId)\n          this.houseDetail.is_favorite = true\n          uni.showToast({\n            title: '收藏成功',\n            icon: 'none'\n          })\n        }\n      } catch (error) {\n        console.error('收藏操作失败：', error)\n      }\n    },\n    \n    // 联系房东\n    contactOwner() {\n      // 检查登录状态\n      const token = uni.getStorageSync('token')\n      if (!token) {\n        uni.showModal({\n          title: '提示',\n          content: '请先登录后再联系房东',\n          success: (res) => {\n            if (res.confirm) {\n              uni.navigateTo({\n                url: '/pages/auth/login'\n              })\n            }\n          }\n        })\n        return\n      }\n      \n      // 拨打电话或跳转聊天页面\n      if (this.houseDetail.owner?.phone) {\n        uni.makePhoneCall({\n          phoneNumber: this.houseDetail.owner.phone\n        })\n      } else {\n        uni.showToast({\n          title: '暂无联系方式',\n          icon: 'none'\n        })\n      }\n    },\n    \n    // 分享房源\n    shareHouse() {\n      // 触发小程序分享\n      uni.showShareMenu({\n        withShareTicket: true\n      })\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.house-detail-page {\n  background-color: $bg-color-page;\n  padding-bottom: 120rpx;\n}\n\n/* 图片轮播 */\n.image-section {\n  position: relative;\n  \n  .image-swiper {\n    height: 500rpx;\n    \n    .house-image {\n      width: 100%;\n      height: 100%;\n    }\n  }\n  \n  .image-count {\n    position: absolute;\n    bottom: $spacing-md;\n    right: $spacing-md;\n    padding: 8rpx 16rpx;\n    background-color: rgba(0, 0, 0, 0.5);\n    color: #fff;\n    font-size: 24rpx;\n    border-radius: 16rpx;\n  }\n}\n\n/* 房源信息 */\n.info-section {\n  background-color: #fff;\n  padding: $spacing-lg;\n  margin-bottom: $spacing-md;\n  \n  .price-info {\n    display: flex;\n    align-items: baseline;\n    margin-bottom: $spacing-md;\n    position: relative;\n    \n    .price {\n      font-size: 48rpx;\n      font-weight: 600;\n      color: $error-color;\n    }\n    \n    .unit {\n      font-size: 28rpx;\n      color: $text-color-tertiary;\n      margin-left: $spacing-xs;\n    }\n    \n    .favorite-btn {\n      position: absolute;\n      right: 0;\n      width: 60rpx;\n      height: 60rpx;\n      display: flex;\n      align-items: center;\n      justify-content: center;\n      background-color: $bg-color-light;\n      border-radius: 50%;\n    }\n  }\n  \n  .title {\n    font-size: 36rpx;\n    font-weight: 600;\n    color: $text-color-primary;\n    margin-bottom: $spacing-md;\n    line-height: 1.4;\n  }\n  \n  .tags {\n    margin-bottom: $spacing-md;\n    \n    .tag {\n      display: inline-block;\n      padding: 8rpx 16rpx;\n      background-color: $bg-color-light;\n      color: $text-color-secondary;\n      font-size: 24rpx;\n      border-radius: 8rpx;\n      margin-right: $spacing-sm;\n    }\n  }\n  \n  .location {\n    display: flex;\n    align-items: center;\n    \n    .address {\n      margin-left: 8rpx;\n      font-size: 28rpx;\n      color: $text-color-secondary;\n    }\n  }\n}\n\n/* 房源配置 */\n.config-section {\n  background-color: #fff;\n  padding: $spacing-lg;\n  margin-bottom: $spacing-md;\n  \n  .section-title {\n    font-size: 32rpx;\n    font-weight: 600;\n    color: $text-color-primary;\n    margin-bottom: $spacing-lg;\n  }\n  \n  .config-grid {\n    display: grid;\n    grid-template-columns: 1fr 1fr;\n    gap: $spacing-lg;\n    \n    .config-item {\n      display: flex;\n      justify-content: space-between;\n      align-items: center;\n      \n      .config-label {\n        font-size: 28rpx;\n        color: $text-color-secondary;\n      }\n      \n      .config-value {\n        font-size: 28rpx;\n        color: $text-color-primary;\n        font-weight: 500;\n      }\n    }\n  }\n}\n\n/* 房源描述 */\n.desc-section {\n  background-color: #fff;\n  padding: $spacing-lg;\n  margin-bottom: $spacing-md;\n  \n  .section-title {\n    font-size: 32rpx;\n    font-weight: 600;\n    color: $text-color-primary;\n    margin-bottom: $spacing-lg;\n  }\n  \n  .desc-content {\n    font-size: 28rpx;\n    color: $text-color-secondary;\n    line-height: 1.6;\n  }\n}\n\n/* 房东信息 */\n.owner-section {\n  background-color: #fff;\n  padding: $spacing-lg;\n  margin-bottom: $spacing-md;\n  \n  .section-title {\n    font-size: 32rpx;\n    font-weight: 600;\n    color: $text-color-primary;\n    margin-bottom: $spacing-lg;\n  }\n  \n  .owner-info {\n    display: flex;\n    align-items: center;\n    \n    .owner-avatar {\n      width: 80rpx;\n      height: 80rpx;\n      border-radius: 50%;\n      margin-right: $spacing-md;\n    }\n    \n    .owner-details {\n      flex: 1;\n      \n      .owner-name {\n        display: block;\n        font-size: 30rpx;\n        font-weight: 500;\n        color: $text-color-primary;\n        margin-bottom: 8rpx;\n      }\n      \n      .owner-desc {\n        font-size: 24rpx;\n        color: $text-color-tertiary;\n      }\n    }\n    \n    .contact-btn {\n      padding: $spacing-sm $spacing-md;\n      background-color: $primary-color;\n      color: #fff;\n      font-size: 26rpx;\n      border: none;\n      border-radius: $border-radius-base;\n    }\n  }\n}\n\n/* 底部操作栏 */\n.bottom-bar {\n  position: fixed;\n  bottom: 0;\n  left: 0;\n  right: 0;\n  display: flex;\n  background-color: #fff;\n  border-top: 1px solid $border-color-light;\n  padding: $spacing-md;\n  z-index: 100;\n  \n  .share-btn,\n  .favorite-btn {\n    display: flex;\n    flex-direction: column;\n    align-items: center;\n    justify-content: center;\n    width: 120rpx;\n    height: 80rpx;\n    background-color: transparent;\n    border: none;\n    font-size: 22rpx;\n    color: $text-color-secondary;\n    margin-right: $spacing-md;\n    \n    text {\n      margin-top: 4rpx;\n    }\n  }\n  \n  .contact-btn {\n    flex: 1;\n    height: 80rpx;\n    background-color: $primary-color;\n    color: #fff;\n    font-size: 30rpx;\n    border: none;\n    border-radius: $border-radius-base;\n  }\n}\n\n/* 加载状态 */\n.loading-state {\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  height: 400rpx;\n}\n</style>\n", "import mod from \"-!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./detail.vue?vue&type=style&index=0&id=4265f319&lang=scss&scoped=true&\"; export default mod; export * from \"-!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./detail.vue?vue&type=style&index=0&id=4265f319&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754033370510\n      var cssReload = require(\"D:/web/wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}
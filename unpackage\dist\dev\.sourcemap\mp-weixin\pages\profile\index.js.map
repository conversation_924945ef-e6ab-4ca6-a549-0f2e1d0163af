{"version": 3, "sources": ["uni-app:///pages/profile/index.vue", "webpack:///D:/web/project/前端10/pages/profile/index.vue?4ff0", "webpack:///D:/web/project/前端10/pages/profile/index.vue?8d38", "uni-app:///main.js", "webpack:///D:/web/project/前端10/pages/profile/index.vue?9d0e", "webpack:///D:/web/project/前端10/pages/profile/index.vue?4eb9", "webpack:///D:/web/project/前端10/pages/profile/index.vue?129b", "webpack:///D:/web/project/前端10/pages/profile/index.vue?14a9"], "names": ["data", "userInfo", "stats", "myHouses", "favorites", "viewHistory", "onShow", "methods", "loadUserInfo", "loadStats", "console", "goToLogin", "uni", "url", "editProfile", "goToMyHouses", "goToFavorites", "goToHistory", "contactService", "phoneNumber", "goToFeedback", "goToAbout", "goToSettings", "checkLogin", "title", "content", "success", "logout", "icon", "wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page"], "mappings": ";;;;;;;;;;;;;;;;;;;AAoHA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eAEA;EACAA;IACA;MACAC;MACAC;QACAC;QACAC;QACAC;MACA;IACA;EACA;EAEAC;IACA;IACA;EACA;EAEAC;IACA;IACAC;MACA;MACA;QACA;MACA;IACA;IAEA;IACAC;MAAA;MAAA;QAAA;UAAA;YAAA;cAAA;gBAAA,IACA;kBAAA;kBAAA;gBAAA;gBAAA;cAAA;gBAEA;kBACA;kBACA;kBACA;;kBAEA;kBACA;oBACAN;oBACAC;oBACAC;kBACA;gBACA;kBACAK;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IAEA;IACAC;MACAC;QACAC;MACA;IACA;IAEA;IACAC;MACAF;QACAC;MACA;IACA;IAEA;IACAE;MACA;MACAH;QACAC;MACA;IACA;IAEA;IACAG;MACA;MACAJ;QACAC;MACA;IACA;IAEA;IACAI;MACA;MACAL;QACAC;MACA;IACA;IAEA;IACAK;MACAN;QACAO;MACA;IACA;IAEA;IACAC;MACAR;QACAC;MACA;IACA;IAEA;IACAQ;MACAT;QACAC;MACA;IACA;IAEA;IACAS;MACAV;QACAC;MACA;IACA;IAEA;IACAU;MAAA;MACA;QACAX;UACAY;UACAC;UACAC;YACA;cACA;YACA;UACA;QACA;QACA;MACA;MACA;IACA;IAEA;IACAC;MAAA;MACAf;QACAY;QACAC;QACAC;UACA;YACA;YACAd;YACAA;;YAEA;YACA;YACA;cACAT;cACAC;cACAC;YACA;YAEAO;cACAY;cACAI;YACA;UACA;QACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACnRA;AAAA;AAAA;AAAA;AAA2tC,CAAgB,ipCAAG,EAAC,C;;;;;;;;;;;ACA/uC;AACA,OAAO,KAAU,EAAE,kBAKd;;;;;;;;;;;;;;;;ACNL;AAE2D;AAC3D;AACA;AAHA;AACAC,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,eAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAA8H;AAC9H;AACyD;AACL;AACsC;;;AAG1F;AACyK;AACzK,gBAAgB,gLAAU;AAC1B,EAAE,2EAAM;AACR,EAAE,4FAAM;AACR,EAAE,qGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,gGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACRA;AAAA;AAAA;AAAA;AAAgoB,CAAgB,8nBAAG,EAAC,C", "file": "pages/profile/index.js", "sourcesContent": ["<template>\n  <view class=\"profile-page\">\n    <!-- 用户信息 -->\n    <view class=\"user-section\">\n      <view class=\"user-info\" @click=\"goToLogin\" v-if=\"!userInfo.nickname\">\n        <image class=\"avatar\" src=\"/static/default-avatar.png\"></image>\n        <view class=\"user-details\">\n          <text class=\"nickname\">点击登录</text>\n          <text class=\"desc\">登录后享受更多服务</text>\n        </view>\n        <uni-icons type=\"right\" size=\"16\" color=\"#999\"></uni-icons>\n      </view>\n      \n      <view class=\"user-info\" v-else>\n        <image class=\"avatar\" :src=\"userInfo.avatar || '/static/default-avatar.png'\"></image>\n        <view class=\"user-details\">\n          <text class=\"nickname\">{{ userInfo.nickname }}</text>\n          <text class=\"desc\">{{ userInfo.phone || '未绑定手机号' }}</text>\n        </view>\n        <view class=\"edit-btn\" @click=\"editProfile\">\n          <uni-icons type=\"compose\" size=\"16\" color=\"#999\"></uni-icons>\n        </view>\n      </view>\n    </view>\n\n    <!-- 数据统计 -->\n    <view class=\"stats-section\" v-if=\"userInfo.nickname\">\n      <view class=\"stats-grid\">\n        <view class=\"stats-item\" @click=\"goToMyHouses\">\n          <text class=\"stats-num\">{{ stats.myHouses }}</text>\n          <text class=\"stats-label\">我的发布</text>\n        </view>\n        <view class=\"stats-item\" @click=\"goToFavorites\">\n          <text class=\"stats-num\">{{ stats.favorites }}</text>\n          <text class=\"stats-label\">我的收藏</text>\n        </view>\n        <view class=\"stats-item\" @click=\"goToHistory\">\n          <text class=\"stats-num\">{{ stats.viewHistory }}</text>\n          <text class=\"stats-label\">浏览记录</text>\n        </view>\n      </view>\n    </view>\n\n    <!-- 功能菜单 -->\n    <view class=\"menu-section\">\n      <view class=\"menu-group\">\n        <view class=\"menu-item\" @click=\"goToMyHouses\">\n          <view class=\"menu-left\">\n            <uni-icons type=\"home\" size=\"20\" color=\"#007AFF\"></uni-icons>\n            <text class=\"menu-text\">我的发布</text>\n          </view>\n          <uni-icons type=\"right\" size=\"14\" color=\"#999\"></uni-icons>\n        </view>\n        \n        <view class=\"menu-item\" @click=\"goToFavorites\">\n          <view class=\"menu-left\">\n            <uni-icons type=\"heart\" size=\"20\" color=\"#FF4D4F\"></uni-icons>\n            <text class=\"menu-text\">我的收藏</text>\n          </view>\n          <uni-icons type=\"right\" size=\"14\" color=\"#999\"></uni-icons>\n        </view>\n        \n        <view class=\"menu-item\" @click=\"goToHistory\">\n          <view class=\"menu-left\">\n            <uni-icons type=\"eye\" size=\"20\" color=\"#52C41A\"></uni-icons>\n            <text class=\"menu-text\">浏览记录</text>\n          </view>\n          <uni-icons type=\"right\" size=\"14\" color=\"#999\"></uni-icons>\n        </view>\n      </view>\n      \n      <view class=\"menu-group\">\n        <view class=\"menu-item\" @click=\"contactService\">\n          <view class=\"menu-left\">\n            <uni-icons type=\"chatbubble\" size=\"20\" color=\"#FAAD14\"></uni-icons>\n            <text class=\"menu-text\">联系客服</text>\n          </view>\n          <uni-icons type=\"right\" size=\"14\" color=\"#999\"></uni-icons>\n        </view>\n        \n        <view class=\"menu-item\" @click=\"goToFeedback\">\n          <view class=\"menu-left\">\n            <uni-icons type=\"compose\" size=\"20\" color=\"#722ED1\"></uni-icons>\n            <text class=\"menu-text\">意见反馈</text>\n          </view>\n          <uni-icons type=\"right\" size=\"14\" color=\"#999\"></uni-icons>\n        </view>\n        \n        <view class=\"menu-item\" @click=\"goToAbout\">\n          <view class=\"menu-left\">\n            <uni-icons type=\"info\" size=\"20\" color=\"#13C2C2\"></uni-icons>\n            <text class=\"menu-text\">关于我们</text>\n          </view>\n          <uni-icons type=\"right\" size=\"14\" color=\"#999\"></uni-icons>\n        </view>\n      </view>\n      \n      <view class=\"menu-group\">\n        <view class=\"menu-item\" @click=\"goToSettings\">\n          <view class=\"menu-left\">\n            <uni-icons type=\"gear\" size=\"20\" color=\"#999\"></uni-icons>\n            <text class=\"menu-text\">设置</text>\n          </view>\n          <uni-icons type=\"right\" size=\"14\" color=\"#999\"></uni-icons>\n        </view>\n      </view>\n    </view>\n\n    <!-- 退出登录 -->\n    <view class=\"logout-section\" v-if=\"userInfo.nickname\">\n      <button class=\"logout-btn\" @click=\"logout\">退出登录</button>\n    </view>\n  </view>\n</template>\n\n<script>\nimport { authAPI } from '@/utils/request.js'\n\nexport default {\n  data() {\n    return {\n      userInfo: {},\n      stats: {\n        myHouses: 0,\n        favorites: 0,\n        viewHistory: 0\n      }\n    }\n  },\n  \n  onShow() {\n    this.loadUserInfo()\n    this.loadStats()\n  },\n  \n  methods: {\n    // 加载用户信息\n    loadUserInfo() {\n      const userInfo = uni.getStorageSync('userInfo')\n      if (userInfo) {\n        this.userInfo = userInfo\n      }\n    },\n    \n    // 加载统计数据\n    async loadStats() {\n      if (!this.userInfo.nickname) return\n      \n      try {\n        // 这里可以调用相应的API获取统计数据\n        // const result = await authAPI.getUserStats()\n        // this.stats = result.data\n        \n        // 临时模拟数据\n        this.stats = {\n          myHouses: 3,\n          favorites: 8,\n          viewHistory: 15\n        }\n      } catch (error) {\n        console.error('加载统计数据失败：', error)\n      }\n    },\n    \n    // 跳转到登录页面\n    goToLogin() {\n      uni.navigateTo({\n        url: '/pages/auth/login'\n      })\n    },\n    \n    // 编辑个人资料\n    editProfile() {\n      uni.navigateTo({\n        url: '/pages/profile/edit'\n      })\n    },\n    \n    // 跳转到我的发布\n    goToMyHouses() {\n      if (!this.checkLogin()) return\n      uni.navigateTo({\n        url: '/pages/publish/my'\n      })\n    },\n    \n    // 跳转到我的收藏\n    goToFavorites() {\n      if (!this.checkLogin()) return\n      uni.navigateTo({\n        url: '/pages/profile/favorite'\n      })\n    },\n    \n    // 跳转到浏览记录\n    goToHistory() {\n      if (!this.checkLogin()) return\n      uni.navigateTo({\n        url: '/pages/profile/history'\n      })\n    },\n    \n    // 联系客服\n    contactService() {\n      uni.makePhoneCall({\n        phoneNumber: '************'\n      })\n    },\n    \n    // 意见反馈\n    goToFeedback() {\n      uni.navigateTo({\n        url: '/pages/profile/feedback'\n      })\n    },\n    \n    // 关于我们\n    goToAbout() {\n      uni.navigateTo({\n        url: '/pages/profile/about'\n      })\n    },\n    \n    // 设置\n    goToSettings() {\n      uni.navigateTo({\n        url: '/pages/profile/settings'\n      })\n    },\n    \n    // 检查登录状态\n    checkLogin() {\n      if (!this.userInfo.nickname) {\n        uni.showModal({\n          title: '提示',\n          content: '请先登录',\n          success: (res) => {\n            if (res.confirm) {\n              this.goToLogin()\n            }\n          }\n        })\n        return false\n      }\n      return true\n    },\n    \n    // 退出登录\n    logout() {\n      uni.showModal({\n        title: '提示',\n        content: '确定要退出登录吗？',\n        success: (res) => {\n          if (res.confirm) {\n            // 清除本地存储\n            uni.removeStorageSync('token')\n            uni.removeStorageSync('userInfo')\n            \n            // 重置数据\n            this.userInfo = {}\n            this.stats = {\n              myHouses: 0,\n              favorites: 0,\n              viewHistory: 0\n            }\n            \n            uni.showToast({\n              title: '已退出登录',\n              icon: 'success'\n            })\n          }\n        }\n      })\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.profile-page {\n  background-color: $bg-color-page;\n  min-height: 100vh;\n}\n\n/* 用户信息 */\n.user-section {\n  background-color: #fff;\n  margin-bottom: $spacing-md;\n  \n  .user-info {\n    display: flex;\n    align-items: center;\n    padding: $spacing-xl $spacing-lg;\n    \n    .avatar {\n      width: 120rpx;\n      height: 120rpx;\n      border-radius: 50%;\n      margin-right: $spacing-lg;\n    }\n    \n    .user-details {\n      flex: 1;\n      \n      .nickname {\n        display: block;\n        font-size: 36rpx;\n        font-weight: 600;\n        color: $text-color-primary;\n        margin-bottom: $spacing-xs;\n      }\n      \n      .desc {\n        font-size: 26rpx;\n        color: $text-color-tertiary;\n      }\n    }\n    \n    .edit-btn {\n      width: 60rpx;\n      height: 60rpx;\n      display: flex;\n      align-items: center;\n      justify-content: center;\n      background-color: $bg-color-light;\n      border-radius: 50%;\n    }\n  }\n}\n\n/* 数据统计 */\n.stats-section {\n  background-color: #fff;\n  margin-bottom: $spacing-md;\n  padding: $spacing-lg;\n  \n  .stats-grid {\n    display: flex;\n    \n    .stats-item {\n      flex: 1;\n      display: flex;\n      flex-direction: column;\n      align-items: center;\n      \n      .stats-num {\n        font-size: 48rpx;\n        font-weight: 600;\n        color: $primary-color;\n        margin-bottom: $spacing-xs;\n      }\n      \n      .stats-label {\n        font-size: 26rpx;\n        color: $text-color-secondary;\n      }\n    }\n  }\n}\n\n/* 功能菜单 */\n.menu-section {\n  .menu-group {\n    background-color: #fff;\n    margin-bottom: $spacing-md;\n    \n    .menu-item {\n      display: flex;\n      align-items: center;\n      justify-content: space-between;\n      padding: $spacing-lg;\n      border-bottom: 1px solid $border-color-lighter;\n      \n      &:last-child {\n        border-bottom: none;\n      }\n      \n      .menu-left {\n        display: flex;\n        align-items: center;\n        \n        .menu-text {\n          margin-left: $spacing-md;\n          font-size: 30rpx;\n          color: $text-color-primary;\n        }\n      }\n    }\n  }\n}\n\n/* 退出登录 */\n.logout-section {\n  padding: $spacing-lg;\n  \n  .logout-btn {\n    width: 100%;\n    height: 88rpx;\n    background-color: #fff;\n    color: $error-color;\n    font-size: 30rpx;\n    border: 1px solid $border-color-base;\n    border-radius: $border-radius-base;\n  }\n}\n</style>\n", "import mod from \"-!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&id=14bc1b43&lang=scss&scoped=true&\"; export default mod; export * from \"-!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&id=14bc1b43&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754033370432\n      var cssReload = require(\"D:/web/wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  ", "import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;import '@dcloudio/vue-cli-plugin-uni/packages/uni-cloud/dist/index.js';\nimport Vue from 'vue'\nimport Page from './pages/profile/index.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./index.vue?vue&type=template&id=14bc1b43&scoped=true&\"\nvar renderjs\nimport script from \"./index.vue?vue&type=script&lang=js&\"\nexport * from \"./index.vue?vue&type=script&lang=js&\"\nimport style0 from \"./index.vue?vue&type=style&index=0&id=14bc1b43&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"14bc1b43\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/profile/index.vue\"\nexport default component.exports", "export * from \"-!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=template&id=14bc1b43&scoped=true&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\""], "sourceRoot": ""}
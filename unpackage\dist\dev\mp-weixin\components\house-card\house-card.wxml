<view data-event-opts="{{[['tap',[['onCardClick',['$event']]]]]}}" class="house-card data-v-529060b8" bindtap="__e"><image class="house-image data-v-529060b8" src="{{houseData.images[0]}}" mode="aspectFill"></image><view class="house-info data-v-529060b8"><view class="house-title data-v-529060b8">{{houseData.title}}</view><view class="house-desc data-v-529060b8">{{houseData.description}}</view><block wx:if="{{$root.g0}}"><view class="house-tags data-v-529060b8"><block wx:for="{{$root.l0}}" wx:for-item="tag" wx:for-index="__i0__" wx:key="*this"><text class="tag data-v-529060b8">{{tag}}</text></block></view></block><view class="house-bottom data-v-529060b8"><view class="price data-v-529060b8"><text class="price-num data-v-529060b8">{{houseData.price}}</text><text class="price-unit data-v-529060b8">元/月</text></view><view class="location data-v-529060b8"><uni-icons vue-id="0042a5e2-1" type="location" size="12" color="#999" class="data-v-529060b8" bind:__l="__l"></uni-icons><text class="location-text data-v-529060b8">{{houseData.address}}</text></view></view></view><block wx:if="{{showFavorite}}"><view data-event-opts="{{[['tap',[['onFavoriteClick',['$event']]]]]}}" class="favorite-btn data-v-529060b8" catchtap="__e"><uni-icons vue-id="0042a5e2-2" type="{{houseData.is_favorite?'heart-filled':'heart'}}" size="20" color="{{houseData.is_favorite?'#FF4D4F':'#999'}}" class="data-v-529060b8" bind:__l="__l"></uni-icons></view></block></view>
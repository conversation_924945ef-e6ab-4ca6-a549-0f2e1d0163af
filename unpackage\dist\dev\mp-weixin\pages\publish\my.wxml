<view class="my-publish-page data-v-b8d65436"><view class="status-tabs data-v-b8d65436"><block wx:for="{{statusTabs}}" wx:for-item="tab" wx:for-index="index" wx:key="index"><view data-event-opts="{{[['tap',[['switchStatus',['$0'],[[['statusTabs','',index,'value']]]]]]]}}" class="{{['tab-item','data-v-b8d65436',(currentStatus===tab.value)?'active':'']}}" bindtap="__e"><text class="tab-text data-v-b8d65436">{{tab.label}}</text><block wx:if="{{tab.count>0}}"><text class="tab-count data-v-b8d65436">{{tab.count}}</text></block></view></block></view><scroll-view class="house-scroll data-v-b8d65436" scroll-y="{{true}}" refresher-enabled="{{true}}" refresher-triggered="{{refreshing}}" data-event-opts="{{[['scrolltolower',[['loadMore',['$event']]]],['refresherrefresh',[['onRefresh',['$event']]]]]}}" bindscrolltolower="__e" bindrefresherrefresh="__e"><view class="house-list data-v-b8d65436"><block wx:for="{{$root.l0}}" wx:for-item="house" wx:for-index="__i0__" wx:key="_id"><view class="house-item data-v-b8d65436"><image class="house-image data-v-b8d65436" src="{{house.$orig.images[0]}}" mode="aspectFill"></image><view class="house-info data-v-b8d65436"><view class="house-title data-v-b8d65436">{{house.$orig.title}}</view><view class="house-desc data-v-b8d65436">{{house.$orig.description}}</view><view class="house-meta data-v-b8d65436"><text class="price data-v-b8d65436">{{house.$orig.price+"元/月"}}</text><text class="{{house.m0}}">{{''+house.m1+''}}</text></view><view class="house-stats data-v-b8d65436"><text class="stat-item data-v-b8d65436">{{"浏览 "+(house.$orig.view_count||0)}}</text><text class="stat-item data-v-b8d65436">{{"收藏 "+(house.$orig.favorite_count||0)}}</text><text class="publish-time data-v-b8d65436">{{house.m2}}</text></view></view><view class="action-btns data-v-b8d65436"><button data-event-opts="{{[['tap',[['viewHouse',['$0'],[[['houseList','_id',house.$orig._id]]]]]]]}}" class="action-btn view-btn data-v-b8d65436" bindtap="__e">查看</button><block wx:if="{{house.$orig.status!=='approved'}}"><button data-event-opts="{{[['tap',[['editHouse',['$0'],[[['houseList','_id',house.$orig._id]]]]]]]}}" class="action-btn edit-btn data-v-b8d65436" bindtap="__e">编辑</button></block><button data-event-opts="{{[['tap',[['deleteHouse',['$0'],[[['houseList','_id',house.$orig._id]]]]]]]}}" class="action-btn delete-btn data-v-b8d65436" bindtap="__e">删除</button></view></view></block></view><block wx:if="{{$root.g0>0}}"><view class="loading-more data-v-b8d65436"><uni-load-more vue-id="61f3307e-1" status="{{loadingStatus}}" class="data-v-b8d65436" bind:__l="__l"></uni-load-more></view></block><block wx:if="{{$root.g1}}"><view class="empty-state data-v-b8d65436"><image class="empty-image data-v-b8d65436" src="/static/empty/no-publish.png"></image><text class="empty-text data-v-b8d65436">{{$root.m3}}</text><block wx:if="{{currentStatus==='all'}}"><button data-event-opts="{{[['tap',[['goToPublish',['$event']]]]]}}" class="publish-btn data-v-b8d65436" bindtap="__e">立即发布</button></block></view></block></scroll-view><view data-event-opts="{{[['tap',[['goToPublish',['$event']]]]]}}" class="float-publish-btn data-v-b8d65436" bindtap="__e"><uni-icons vue-id="61f3307e-2" type="plus" size="24" color="#fff" class="data-v-b8d65436" bind:__l="__l"></uni-icons></view></view>
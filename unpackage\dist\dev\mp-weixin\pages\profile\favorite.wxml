<view class="favorite-page data-v-069a0a8a"><block wx:if="{{$root.g0>0}}"><view class="header-bar data-v-069a0a8a"><view data-event-opts="{{[['tap',[['toggleSelectAll',['$event']]]]]}}" class="select-all data-v-069a0a8a" bindtap="__e"><uni-icons vue-id="cca644aa-1" type="{{isAllSelected?'checkbox-filled':'checkbox'}}" size="18" color="{{isAllSelected?'#007AFF':'#999'}}" class="data-v-069a0a8a" bind:__l="__l"></uni-icons><text class="select-text data-v-069a0a8a">全选</text></view><block wx:if="{{$root.g1>0}}"><view class="selected-count data-v-069a0a8a">{{'已选择 '+$root.g2+' 个'}}</view></block><block wx:if="{{$root.g3>0}}"><view class="batch-actions data-v-069a0a8a"><button data-event-opts="{{[['tap',[['batchDelete',['$event']]]]]}}" class="batch-btn delete-btn data-v-069a0a8a" bindtap="__e">删除</button></view></block></view></block><scroll-view class="favorite-scroll data-v-069a0a8a" scroll-y="{{true}}" refresher-enabled="{{true}}" refresher-triggered="{{refreshing}}" data-event-opts="{{[['scrolltolower',[['loadMore',['$event']]]],['refresherrefresh',[['onRefresh',['$event']]]]]}}" bindscrolltolower="__e" bindrefresherrefresh="__e"><view class="favorite-list data-v-069a0a8a"><block wx:for="{{$root.l1}}" wx:for-item="house" wx:for-index="__i0__" wx:key="_id"><view data-event-opts="{{[['tap',[['goToDetail',['$0'],[[['favoriteList','_id',house.$orig._id]]]]]]]}}" class="favorite-item data-v-069a0a8a" bindtap="__e"><view data-event-opts="{{[['tap',[['toggleSelect',['$0'],[[['favoriteList','_id',house.$orig._id]]]]]]]}}" class="select-box data-v-069a0a8a" catchtap="__e"><uni-icons vue-id="{{'cca644aa-2-'+__i0__}}" type="{{house.g4?'checkbox-filled':'checkbox'}}" size="18" color="{{house.g5?'#007AFF':'#999'}}" class="data-v-069a0a8a" bind:__l="__l"></uni-icons></view><image class="house-image data-v-069a0a8a" src="{{house.$orig.images[0]}}" mode="aspectFill"></image><view class="house-info data-v-069a0a8a"><view class="house-title data-v-069a0a8a">{{house.$orig.title}}</view><view class="house-desc data-v-069a0a8a">{{house.$orig.description}}</view><block wx:if="{{house.g6}}"><view class="house-tags data-v-069a0a8a"><block wx:for="{{house.l0}}" wx:for-item="tag" wx:for-index="__i1__" wx:key="*this"><text class="tag data-v-069a0a8a">{{tag}}</text></block></view></block><view class="house-bottom data-v-069a0a8a"><view class="price data-v-069a0a8a"><text class="price-num data-v-069a0a8a">{{house.$orig.price}}</text><text class="price-unit data-v-069a0a8a">元/月</text></view><view class="favorite-time data-v-069a0a8a">{{''+house.m0+''}}</view></view></view><view data-event-opts="{{[['tap',[['removeFavorite',['$0'],[[['favoriteList','_id',house.$orig._id]]]]]]]}}" class="action-btn data-v-069a0a8a" catchtap="__e"><uni-icons vue-id="{{'cca644aa-3-'+__i0__}}" type="heart-filled" size="20" color="#FF4D4F" class="data-v-069a0a8a" bind:__l="__l"></uni-icons></view></view></block></view><block wx:if="{{$root.g7>0}}"><view class="loading-more data-v-069a0a8a"><uni-load-more vue-id="cca644aa-4" status="{{loadingStatus}}" class="data-v-069a0a8a" bind:__l="__l"></uni-load-more></view></block><block wx:if="{{$root.g8}}"><view class="empty-state data-v-069a0a8a"><image class="empty-image data-v-069a0a8a" src="/static/empty/no-favorite.png"></image><text class="empty-text data-v-069a0a8a">还没有收藏过房源</text><text class="empty-desc data-v-069a0a8a">去首页看看有什么好房源吧</text><button data-event-opts="{{[['tap',[['goToHome',['$event']]]]]}}" class="browse-btn data-v-069a0a8a" bindtap="__e">去逛逛</button></view></block></scroll-view></view>
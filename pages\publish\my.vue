<template>
  <view class="my-publish-page">
    <!-- 状态筛选 -->
    <view class="status-tabs">
      <view 
        class="tab-item" 
        v-for="(tab, index) in statusTabs" 
        :key="index"
        :class="{ active: currentStatus === tab.value }"
        @click="switchStatus(tab.value)"
      >
        <text class="tab-text">{{ tab.label }}</text>
        <text class="tab-count" v-if="tab.count > 0">{{ tab.count }}</text>
      </view>
    </view>

    <!-- 房源列表 -->
    <scroll-view 
      class="house-scroll" 
      scroll-y 
      @scrolltolower="loadMore"
      refresher-enabled
      @refresherrefresh="onRefresh"
      :refresher-triggered="refreshing"
    >
      <view class="house-list">
        <view 
          class="house-item" 
          v-for="house in houseList" 
          :key="house._id"
        >
          <image class="house-image" :src="house.images[0]" mode="aspectFill"></image>
          <view class="house-info">
            <view class="house-title">{{ house.title }}</view>
            <view class="house-desc">{{ house.description }}</view>
            <view class="house-meta">
              <text class="price">{{ house.price }}元/月</text>
              <text class="status" :class="getStatusClass(house.status)">
                {{ getStatusText(house.status) }}
              </text>
            </view>
            <view class="house-stats">
              <text class="stat-item">浏览 {{ house.view_count || 0 }}</text>
              <text class="stat-item">收藏 {{ house.favorite_count || 0 }}</text>
              <text class="publish-time">{{ formatTime(house.created_at) }}</text>
            </view>
          </view>
          <view class="action-btns">
            <button 
              class="action-btn view-btn" 
              @click="viewHouse(house)"
            >
              查看
            </button>
            <button 
              class="action-btn edit-btn" 
              @click="editHouse(house)"
              v-if="house.status !== 'approved'"
            >
              编辑
            </button>
            <button 
              class="action-btn delete-btn" 
              @click="deleteHouse(house)"
            >
              删除
            </button>
          </view>
        </view>
      </view>

      <!-- 加载状态 -->
      <view class="loading-more" v-if="houseList.length > 0">
        <uni-load-more :status="loadingStatus"></uni-load-more>
      </view>

      <!-- 空状态 -->
      <view class="empty-state" v-if="!loading && houseList.length === 0">
        <image class="empty-image" src="/static/empty/no-publish.png"></image>
        <text class="empty-text">{{ getEmptyText() }}</text>
        <button class="publish-btn" @click="goToPublish" v-if="currentStatus === 'all'">
          立即发布
        </button>
      </view>
    </scroll-view>

    <!-- 悬浮发布按钮 -->
    <view class="float-publish-btn" @click="goToPublish">
      <uni-icons type="plus" size="24" color="#fff"></uni-icons>
    </view>
  </view>
</template>

<script>
import { houseAPI } from '@/utils/request.js'

export default {
  data() {
    return {
      loading: false,
      refreshing: false,
      loadingStatus: 'more',
      currentPage: 1,
      pageSize: 10,
      hasMore: true,
      currentStatus: 'all',
      
      houseList: [],
      
      statusTabs: [
        { label: '全部', value: 'all', count: 0 },
        { label: '审核中', value: 'pending', count: 0 },
        { label: '已通过', value: 'approved', count: 0 },
        { label: '已拒绝', value: 'rejected', count: 0 }
      ]
    }
  },
  
  onLoad() {
    this.loadMyHouses()
  },
  
  onShow() {
    // 从发布页面返回时刷新列表
    this.loadMyHouses(true)
  },
  
  methods: {
    // 加载我的房源
    async loadMyHouses(refresh = false) {
      if (this.loading) return
      
      try {
        this.loading = true
        if (refresh) {
          this.currentPage = 1
          this.hasMore = true
          this.refreshing = true
        }
        
        const params = {
          page: this.currentPage,
          limit: this.pageSize
        }
        
        if (this.currentStatus !== 'all') {
          params.status = this.currentStatus
        }
        
        const result = await houseAPI.getMyHouses(params)
        const newList = result.data.list || []
        
        if (refresh) {
          this.houseList = newList
        } else {
          this.houseList.push(...newList)
        }
        
        // 更新状态统计
        if (result.data.statusCount) {
          this.updateStatusCount(result.data.statusCount)
        }
        
        this.hasMore = newList.length === this.pageSize
        this.loadingStatus = this.hasMore ? 'more' : 'noMore'
        
      } catch (error) {
        console.error('加载我的房源失败：', error)
        this.loadingStatus = 'error'
      } finally {
        this.loading = false
        this.refreshing = false
      }
    },
    
    // 更新状态统计
    updateStatusCount(statusCount) {
      this.statusTabs.forEach(tab => {
        if (tab.value === 'all') {
          tab.count = statusCount.total || 0
        } else {
          tab.count = statusCount[tab.value] || 0
        }
      })
    },
    
    // 切换状态
    switchStatus(status) {
      if (this.currentStatus === status) return
      
      this.currentStatus = status
      this.loadMyHouses(true)
    },
    
    // 加载更多
    loadMore() {
      if (this.hasMore && !this.loading) {
        this.currentPage++
        this.loadingStatus = 'loading'
        this.loadMyHouses()
      }
    },
    
    // 下拉刷新
    onRefresh() {
      this.loadMyHouses(true)
    },
    
    // 查看房源
    viewHouse(house) {
      uni.navigateTo({
        url: `/pages/house/detail?id=${house._id}`
      })
    },
    
    // 编辑房源
    editHouse(house) {
      uni.navigateTo({
        url: `/pages/publish/edit?id=${house._id}`
      })
    },
    
    // 删除房源
    deleteHouse(house) {
      uni.showModal({
        title: '确认删除',
        content: '删除后无法恢复，确定要删除这个房源吗？',
        success: async (res) => {
          if (res.confirm) {
            try {
              await houseAPI.deleteHouse(house._id)
              
              // 从列表中移除
              const index = this.houseList.findIndex(item => item._id === house._id)
              if (index > -1) {
                this.houseList.splice(index, 1)
              }
              
              uni.showToast({
                title: '删除成功',
                icon: 'success'
              })
              
            } catch (error) {
              console.error('删除房源失败：', error)
            }
          }
        }
      })
    },
    
    // 跳转到发布页面
    goToPublish() {
      uni.navigateTo({
        url: '/pages/publish/add'
      })
    },
    
    // 获取状态样式类
    getStatusClass(status) {
      const classMap = {
        'pending': 'status-pending',
        'approved': 'status-approved',
        'rejected': 'status-rejected'
      }
      return classMap[status] || ''
    },
    
    // 获取状态文本
    getStatusText(status) {
      const textMap = {
        'pending': '审核中',
        'approved': '已通过',
        'rejected': '已拒绝'
      }
      return textMap[status] || '未知'
    },
    
    // 格式化时间
    formatTime(timestamp) {
      if (!timestamp) return ''
      
      const date = new Date(timestamp)
      const now = new Date()
      const diff = now - date
      
      if (diff < 60000) { // 1分钟内
        return '刚刚'
      } else if (diff < 3600000) { // 1小时内
        return Math.floor(diff / 60000) + '分钟前'
      } else if (diff < 86400000) { // 1天内
        return Math.floor(diff / 3600000) + '小时前'
      } else if (diff < 2592000000) { // 30天内
        return Math.floor(diff / 86400000) + '天前'
      } else {
        return date.toLocaleDateString()
      }
    },
    
    // 获取空状态文本
    getEmptyText() {
      const textMap = {
        'all': '还没有发布过房源',
        'pending': '没有审核中的房源',
        'approved': '没有已通过的房源',
        'rejected': '没有被拒绝的房源'
      }
      return textMap[this.currentStatus] || '暂无数据'
    }
  }
}
</script>

<style lang="scss" scoped>
.my-publish-page {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: $bg-color-page;
  position: relative;
}

/* 状态筛选 */
.status-tabs {
  display: flex;
  background-color: #fff;
  border-bottom: 1px solid $border-color-light;
  
  .tab-item {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    height: 88rpx;
    position: relative;
    
    .tab-text {
      font-size: 28rpx;
      color: $text-color-secondary;
    }
    
    .tab-count {
      margin-left: 8rpx;
      padding: 2rpx 8rpx;
      background-color: $error-color;
      color: #fff;
      font-size: 20rpx;
      border-radius: 10rpx;
      min-width: 32rpx;
      text-align: center;
    }
    
    &.active {
      .tab-text {
        color: $primary-color;
        font-weight: 600;
      }
      
      &::after {
        content: '';
        position: absolute;
        bottom: 0;
        left: 50%;
        transform: translateX(-50%);
        width: 60rpx;
        height: 4rpx;
        background-color: $primary-color;
        border-radius: 2rpx;
      }
    }
  }
}

/* 房源列表 */
.house-scroll {
  flex: 1;
}

.house-list {
  padding: $spacing-md;
  
  .house-item {
    display: flex;
    background-color: #fff;
    border-radius: $border-radius-large;
    margin-bottom: $spacing-md;
    overflow: hidden;
    box-shadow: $box-shadow-light;
    
    .house-image {
      width: 200rpx;
      height: 150rpx;
      flex-shrink: 0;
    }
    
    .house-info {
      flex: 1;
      padding: $spacing-md;
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      
      .house-title {
        font-size: 28rpx;
        font-weight: 600;
        color: $text-color-primary;
        margin-bottom: $spacing-xs;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
      
      .house-desc {
        font-size: 24rpx;
        color: $text-color-tertiary;
        margin-bottom: $spacing-sm;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
      
      .house-meta {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: $spacing-sm;
        
        .price {
          font-size: 28rpx;
          font-weight: 600;
          color: $error-color;
        }
        
        .status {
          padding: 4rpx 12rpx;
          font-size: 20rpx;
          border-radius: 8rpx;
          
          &.status-pending {
            background-color: rgba($warning-color, 0.1);
            color: $warning-color;
          }
          
          &.status-approved {
            background-color: rgba($success-color, 0.1);
            color: $success-color;
          }
          
          &.status-rejected {
            background-color: rgba($error-color, 0.1);
            color: $error-color;
          }
        }
      }
      
      .house-stats {
        display: flex;
        align-items: center;
        
        .stat-item {
          font-size: 22rpx;
          color: $text-color-tertiary;
          margin-right: $spacing-md;
        }
        
        .publish-time {
          margin-left: auto;
          font-size: 22rpx;
          color: $text-color-quaternary;
        }
      }
    }
    
    .action-btns {
      display: flex;
      flex-direction: column;
      justify-content: center;
      padding: $spacing-md;
      gap: $spacing-sm;
      
      .action-btn {
        width: 120rpx;
        height: 60rpx;
        font-size: 24rpx;
        border: none;
        border-radius: $border-radius-base;
        
        &.view-btn {
          background-color: $primary-color;
          color: #fff;
        }
        
        &.edit-btn {
          background-color: $warning-color;
          color: #fff;
        }
        
        &.delete-btn {
          background-color: $bg-color-light;
          color: $error-color;
        }
      }
    }
  }
}

/* 加载状态 */
.loading-more {
  padding: $spacing-lg;
}

/* 空状态 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 120rpx $spacing-lg;
  
  .empty-image {
    width: 200rpx;
    height: 200rpx;
    margin-bottom: $spacing-lg;
  }
  
  .empty-text {
    font-size: 28rpx;
    color: $text-color-tertiary;
    margin-bottom: $spacing-lg;
  }
  
  .publish-btn {
    width: 200rpx;
    height: 72rpx;
    background-color: $primary-color;
    color: #fff;
    font-size: 26rpx;
    border: none;
    border-radius: $border-radius-base;
  }
}

/* 悬浮发布按钮 */
.float-publish-btn {
  position: fixed;
  bottom: 120rpx;
  right: $spacing-lg;
  width: 100rpx;
  height: 100rpx;
  background-color: $primary-color;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: $box-shadow-base;
  z-index: 100;
}
</style>

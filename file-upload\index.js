'use strict';

const uniID = require('uni-id-common');

exports.main = async (event, context) => {
  console.log('file-upload 云函数被调用，参数：', event);
  
  const { action } = event;
  
  try {
    switch (action) {
      case 'uploadImage':
        return await uploadImage(event);
      case 'deleteFile':
        return await deleteFile(event);
      case 'getUploadToken':
        return await getUploadToken(event);
      default:
        return {
          code: 1001,
          message: '不支持的操作类型',
          data: null
        };
    }
  } catch (error) {
    console.error('file-upload 云函数执行错误：', error);
    return {
      code: 1005,
      message: error.message || '操作失败',
      data: null
    };
  }
};

// 上传图片
async function uploadImage(event) {
  const { file, type = 'house' } = event;
  
  // 验证用户登录
  const uniIdIns = uniID.createInstance({ context: this });
  const checkTokenResult = await uniIdIns.checkToken(event.uniIdToken);
  
  if (checkTokenResult.errCode !== 0) {
    return {
      code: 1002,
      message: '用户未登录',
      data: null
    };
  }
  
  if (!file) {
    return {
      code: 1001,
      message: '缺少文件参数',
      data: null
    };
  }
  
  try {
    // 验证文件类型
    const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp'];
    if (!allowedTypes.includes(file.type)) {
      return {
        code: 4002,
        message: '不支持的文件格式，仅支持 JPG、PNG、WEBP 格式',
        data: null
      };
    }
    
    // 验证文件大小（5MB）
    const maxSize = 5 * 1024 * 1024;
    if (file.size > maxSize) {
      return {
        code: 4001,
        message: '文件大小不能超过5MB',
        data: null
      };
    }
    
    // 生成文件名
    const timestamp = Date.now();
    const randomStr = Math.random().toString(36).substring(2);
    const fileExtension = file.name.split('.').pop();
    const fileName = `${type}/${timestamp}_${randomStr}.${fileExtension}`;
    
    // 上传到云存储
    const uploadResult = await uniCloud.uploadFile({
      cloudPath: fileName,
      fileContent: file.buffer || file
    });
    
    if (!uploadResult.fileID) {
      return {
        code: 4001,
        message: '文件上传失败',
        data: null
      };
    }
    
    // 获取文件访问URL
    const getUrlResult = await uniCloud.getTempFileURL({
      fileList: [uploadResult.fileID]
    });
    
    let fileUrl = '';
    if (getUrlResult.fileList && getUrlResult.fileList.length > 0) {
      fileUrl = getUrlResult.fileList[0].tempFileURL;
    }
    
    return {
      code: 0,
      message: '上传成功',
      data: {
        url: fileUrl,
        file_id: uploadResult.fileID,
        file_name: fileName
      }
    };
    
  } catch (error) {
    console.error('文件上传错误：', error);
    return {
      code: 4001,
      message: '文件上传失败',
      data: null
    };
  }
}

// 删除文件
async function deleteFile(event) {
  const { file_id } = event;
  
  // 验证用户登录
  const uniIdIns = uniID.createInstance({ context: this });
  const checkTokenResult = await uniIdIns.checkToken(event.uniIdToken);
  
  if (checkTokenResult.errCode !== 0) {
    return {
      code: 1002,
      message: '用户未登录',
      data: null
    };
  }
  
  if (!file_id) {
    return {
      code: 1001,
      message: '缺少文件ID',
      data: null
    };
  }
  
  try {
    // 删除云存储文件
    const deleteResult = await uniCloud.deleteFile({
      fileList: [file_id]
    });
    
    if (deleteResult.fileList && deleteResult.fileList[0].code === 'SUCCESS') {
      return {
        code: 0,
        message: '删除成功',
        data: null
      };
    } else {
      return {
        code: 1005,
        message: '删除失败',
        data: null
      };
    }
    
  } catch (error) {
    console.error('文件删除错误：', error);
    return {
      code: 1005,
      message: '文件删除失败',
      data: null
    };
  }
}

// 获取上传凭证（用于前端直传）
async function getUploadToken(event) {
  const { type = 'house' } = event;
  
  // 验证用户登录
  const uniIdIns = uniID.createInstance({ context: this });
  const checkTokenResult = await uniIdIns.checkToken(event.uniIdToken);
  
  if (checkTokenResult.errCode !== 0) {
    return {
      code: 1002,
      message: '用户未登录',
      data: null
    };
  }
  
  try {
    // 生成上传路径前缀
    const timestamp = Date.now();
    const userId = checkTokenResult.uid;
    const pathPrefix = `${type}/${userId}/${timestamp}`;
    
    return {
      code: 0,
      message: '获取成功',
      data: {
        path_prefix: pathPrefix,
        max_size: 5 * 1024 * 1024, // 5MB
        allowed_types: ['image/jpeg', 'image/jpg', 'image/png', 'image/webp']
      }
    };
    
  } catch (error) {
    console.error('获取上传凭证错误：', error);
    return {
      code: 1005,
      message: '获取上传凭证失败',
      data: null
    };
  }
}

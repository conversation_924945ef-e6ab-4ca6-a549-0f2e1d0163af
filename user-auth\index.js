'use strict';

const uniID = require('uni-id-common');

exports.main = async (event, context) => {
  console.log('user-auth 云函数被调用，参数：', event);
  
  const { action } = event;
  
  // 统一返回格式
  const response = {
    code: 0,
    message: 'success',
    data: null
  };
  
  try {
    switch (action) {
      case 'login':
        return await login(event);
      case 'getUserInfo':
        return await getUserInfo(event);
      case 'updateUserInfo':
        return await updateUserInfo(event);
      default:
        return {
          code: 1001,
          message: '不支持的操作类型',
          data: null
        };
    }
  } catch (error) {
    console.error('user-auth 云函数执行错误：', error);
    return {
      code: 1005,
      message: error.message || '操作失败',
      data: null
    };
  }
};

// 微信登录
async function login(event) {
  const { code } = event;
  
  if (!code) {
    return {
      code: 1001,
      message: '缺少微信登录code',
      data: null
    };
  }
  
  try {
    // 使用 uni-id 进行微信登录
    const uniIdIns = uniID.createInstance({
      context: this
    });
    
    const loginResult = await uniIdIns.loginByWeixin({
      code,
      platform: 'mp-weixin'
    });
    
    if (loginResult.errCode !== 0) {
      return {
        code: 2001,
        message: loginResult.errMsg || '微信登录失败',
        data: null
      };
    }
    
    // 获取用户信息
    const db = uniCloud.database();
    const userCollection = db.collection('uni-id-users');
    
    const userInfo = await userCollection.doc(loginResult.uid).get();
    
    return {
      code: 0,
      message: '登录成功',
      data: {
        token: loginResult.token,
        userInfo: {
          _id: loginResult.uid,
          nickname: userInfo.data[0]?.nickname || '',
          avatar: userInfo.data[0]?.avatar || '',
          phone: userInfo.data[0]?.mobile || ''
        }
      }
    };
    
  } catch (error) {
    console.error('微信登录错误：', error);
    return {
      code: 2001,
      message: '微信登录失败',
      data: null
    };
  }
}

// 获取用户信息
async function getUserInfo(event) {
  const uniIdIns = uniID.createInstance({
    context: this
  });
  
  // 验证token
  const checkTokenResult = await uniIdIns.checkToken(event.uniIdToken);
  
  if (checkTokenResult.errCode !== 0) {
    return {
      code: 1002,
      message: '用户未登录',
      data: null
    };
  }
  
  const uid = checkTokenResult.uid;
  
  try {
    const db = uniCloud.database();
    const userCollection = db.collection('uni-id-users');
    
    const userResult = await userCollection.doc(uid).get();
    
    if (userResult.data.length === 0) {
      return {
        code: 1004,
        message: '用户不存在',
        data: null
      };
    }
    
    const userInfo = userResult.data[0];
    
    // 获取用户收藏的房源
    const houseCollection = db.collection('house');
    const favoriteHouses = await houseCollection
      .where({
        _id: db.command.in(userInfo.favorites || [])
      })
      .field({
        _id: true,
        title: true,
        price: true,
        images: true
      })
      .get();
    
    return {
      code: 0,
      message: '获取成功',
      data: {
        _id: userInfo._id,
        nickname: userInfo.nickname || '',
        avatar: userInfo.avatar || '',
        phone: userInfo.mobile || '',
        favorites: favoriteHouses.data || [],
        publish_count: userInfo.publish_count || 0
      }
    };
    
  } catch (error) {
    console.error('获取用户信息错误：', error);
    return {
      code: 1005,
      message: '获取用户信息失败',
      data: null
    };
  }
}

// 更新用户信息
async function updateUserInfo(event) {
  const { phone, nickname } = event;
  
  const uniIdIns = uniID.createInstance({
    context: this
  });
  
  // 验证token
  const checkTokenResult = await uniIdIns.checkToken(event.uniIdToken);
  
  if (checkTokenResult.errCode !== 0) {
    return {
      code: 1002,
      message: '用户未登录',
      data: null
    };
  }
  
  const uid = checkTokenResult.uid;
  
  try {
    const db = uniCloud.database();
    const userCollection = db.collection('uni-id-users');
    
    const updateData = {
      updated_at: new Date()
    };
    
    if (phone) {
      updateData.mobile = phone;
    }
    
    if (nickname) {
      updateData.nickname = nickname;
    }
    
    await userCollection.doc(uid).update(updateData);
    
    return {
      code: 0,
      message: '更新成功',
      data: null
    };
    
  } catch (error) {
    console.error('更新用户信息错误：', error);
    return {
      code: 1005,
      message: '更新用户信息失败',
      data: null
    };
  }
}

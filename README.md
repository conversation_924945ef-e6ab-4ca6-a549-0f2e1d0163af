# 找房租房小程序

一个基于 uniapp + Vue2 + uniCloud 的房屋租赁小程序前端项目。

## 项目特色

- 🏠 **完整的房源管理** - 房源发布、编辑、审核、搜索
- 👤 **用户系统** - 微信登录、个人中心、收藏功能
- 📱 **响应式设计** - 适配各种屏幕尺寸
- 🎨 **现代化UI** - 简洁美观的界面设计
- ⚡ **高性能** - 优化的加载速度和用户体验

## 技术栈

- **前端框架**: uniapp + Vue2
- **开发工具**: HBuilderX
- **后端服务**: uniCloud (阿里云版)
- **数据库**: uniCloud 云数据库
- **文件存储**: uniCloud 云存储
- **样式**: SCSS + uni-ui

## 项目结构

```
├── pages/                  # 页面目录
│   ├── index/              # 首页
│   ├── house/              # 房源相关页面
│   │   ├── list.vue        # 房源列表
│   │   ├── detail.vue      # 房源详情
│   │   └── search.vue      # 搜索页面
│   ├── publish/            # 发布相关页面
│   │   ├── add.vue         # 发布房源
│   │   └── my.vue          # 我的发布
│   ├── profile/            # 个人中心
│   │   ├── index.vue       # 个人中心首页
│   │   └── favorite.vue    # 我的收藏
│   └── auth/               # 认证相关
│       └── login.vue       # 登录页面
├── components/             # 组件目录
│   └── house-card/         # 房源卡片组件
├── utils/                  # 工具类
│   └── request.js          # 网络请求封装
├── static/                 # 静态资源
├── uniCloud-aliyun/        # 云函数目录
└── uni_modules/            # uni模块
```

## 主要功能

### 🏠 房源管理
- 房源列表展示
- 房源详情查看
- 房源搜索筛选
- 房源发布编辑
- 房源收藏功能

### 👤 用户系统
- 微信授权登录
- 手机号登录
- 个人信息管理
- 我的发布管理
- 我的收藏管理

### 🔍 搜索功能
- 关键词搜索
- 搜索历史记录
- 热门搜索推荐
- 搜索建议提示

### 📱 界面特性
- 底部导航栏
- 下拉刷新
- 上拉加载更多
- 图片轮播预览
- 筛选弹窗

## 快速开始

### 环境要求
- HBuilderX 3.0+
- 微信开发者工具
- uniCloud 账号

### 安装步骤

1. **克隆项目**
   ```bash
   git clone [项目地址]
   cd 前端10
   ```

2. **配置 uniCloud**
   - 在 HBuilderX 中打开项目
   - 右键 `uniCloud-aliyun` 目录
   - 选择 "关联云服务空间"
   - 创建或选择已有的云服务空间

3. **配置微信小程序**
   - 修改 `manifest.json` 中的 `mp-weixin.appid`
   - 填入你的微信小程序 AppID

4. **上传云函数**
   - 右键 `uniCloud-aliyun/cloudfunctions` 目录
   - 选择 "上传所有云函数"

5. **运行项目**
   - 点击 HBuilderX 顶部的 "运行" 按钮
   - 选择 "运行到小程序模拟器" > "微信开发者工具"

## 云函数说明

项目包含以下云函数：

- **user-auth**: 用户认证相关功能
- **house-manage**: 房源管理相关功能
- **admin-manage**: 管理员功能
- **file-upload**: 文件上传功能

## 数据库设计

主要数据表：

- **uni-id-users**: 用户信息表
- **house**: 房源信息表
- **admin**: 管理员信息表
- **system_config**: 系统配置表

## 页面路由

| 路径 | 页面 | 说明 |
|------|------|------|
| `/pages/index/index` | 首页 | 房源推荐、搜索入口 |
| `/pages/house/list` | 房源列表 | 房源筛选、列表展示 |
| `/pages/house/detail` | 房源详情 | 房源详细信息 |
| `/pages/house/search` | 搜索页面 | 房源搜索功能 |
| `/pages/publish/add` | 发布房源 | 房源信息填写 |
| `/pages/publish/my` | 我的发布 | 已发布房源管理 |
| `/pages/profile/index` | 个人中心 | 用户信息、功能入口 |
| `/pages/profile/favorite` | 我的收藏 | 收藏房源管理 |
| `/pages/auth/login` | 登录页面 | 微信登录、手机登录 |

## 开发规范

### 代码规范
- 使用 ES6+ 语法
- 组件名使用 PascalCase
- 文件名使用 kebab-case
- 样式使用 SCSS

### 提交规范
- feat: 新功能
- fix: 修复bug
- docs: 文档更新
- style: 代码格式调整
- refactor: 代码重构

## 部署说明

1. **云函数部署**
   - 在 HBuilderX 中上传所有云函数
   - 配置云函数的环境变量

2. **小程序发布**
   - 在微信开发者工具中预览测试
   - 上传代码到微信后台
   - 提交审核并发布

## 注意事项

1. **微信小程序配置**
   - 需要在微信公众平台配置服务器域名
   - 需要配置业务域名（如果使用 webview）

2. **权限申请**
   - 位置权限：用于获取用户位置
   - 相机权限：用于拍照上传

3. **图标资源**
   - 需要准备 tabBar 图标
   - 建议使用 81x81px 的 PNG 格式

## 联系方式

如有问题，请联系开发团队。

## 许可证

MIT License

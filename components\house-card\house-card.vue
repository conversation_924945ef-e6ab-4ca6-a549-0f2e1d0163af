<template>
  <view class="house-card" @click="onCardClick">
    <image class="house-image" :src="houseData.images[0]" mode="aspectFill"></image>
    <view class="house-info">
      <view class="house-title">{{ houseData.title }}</view>
      <view class="house-desc">{{ houseData.description }}</view>
      <view class="house-tags" v-if="houseData.tags && houseData.tags.length > 0">
        <text class="tag" v-for="tag in houseData.tags.slice(0, 3)" :key="tag">{{ tag }}</text>
      </view>
      <view class="house-bottom">
        <view class="price">
          <text class="price-num">{{ houseData.price }}</text>
          <text class="price-unit">元/月</text>
        </view>
        <view class="location">
          <uni-icons type="location" size="12" color="#999"></uni-icons>
          <text class="location-text">{{ houseData.address }}</text>
        </view>
      </view>
    </view>
    <view class="favorite-btn" @click.stop="onFavoriteClick" v-if="showFavorite">
      <uni-icons 
        :type="houseData.is_favorite ? 'heart-filled' : 'heart'" 
        size="20" 
        :color="houseData.is_favorite ? '#FF4D4F' : '#999'"
      ></uni-icons>
    </view>
  </view>
</template>

<script>
export default {
  name: 'HouseCard',
  props: {
    houseData: {
      type: Object,
      required: true,
      default: () => ({
        _id: '',
        title: '',
        description: '',
        price: 0,
        address: '',
        images: [],
        tags: [],
        is_favorite: false
      })
    },
    showFavorite: {
      type: Boolean,
      default: true
    }
  },
  
  methods: {
    onCardClick() {
      this.$emit('click', this.houseData)
    },
    
    onFavoriteClick() {
      this.$emit('favorite', this.houseData)
    }
  }
}
</script>

<style lang="scss" scoped>
.house-card {
  position: relative;
  display: flex;
  background-color: #fff;
  border-radius: $border-radius-large;
  overflow: hidden;
  box-shadow: $box-shadow-light;
  
  .house-image {
    width: 240rpx;
    height: 180rpx;
    flex-shrink: 0;
  }
  
  .house-info {
    flex: 1;
    padding: $spacing-md;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    
    .house-title {
      font-size: 30rpx;
      font-weight: 600;
      color: $text-color-primary;
      margin-bottom: $spacing-xs;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      padding-right: 60rpx;
    }
    
    .house-desc {
      font-size: 24rpx;
      color: $text-color-tertiary;
      margin-bottom: $spacing-xs;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
    
    .house-tags {
      margin-bottom: $spacing-sm;
      
      .tag {
        display: inline-block;
        padding: 4rpx 12rpx;
        background-color: $bg-color-light;
        color: $text-color-secondary;
        font-size: 20rpx;
        border-radius: 8rpx;
        margin-right: $spacing-xs;
      }
    }
    
    .house-bottom {
      display: flex;
      justify-content: space-between;
      align-items: flex-end;
      
      .price {
        .price-num {
          font-size: 32rpx;
          font-weight: 600;
          color: $error-color;
        }
        
        .price-unit {
          font-size: 24rpx;
          color: $text-color-tertiary;
        }
      }
      
      .location {
        display: flex;
        align-items: center;
        
        .location-text {
          margin-left: 4rpx;
          font-size: 22rpx;
          color: $text-color-tertiary;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
          max-width: 160rpx;
        }
      }
    }
  }
  
  .favorite-btn {
    position: absolute;
    top: $spacing-md;
    right: $spacing-md;
    width: 60rpx;
    height: 60rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: rgba(255, 255, 255, 0.9);
    border-radius: 50%;
  }
}
</style>

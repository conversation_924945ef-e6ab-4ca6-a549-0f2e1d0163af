{"version": 3, "sources": ["webpack:///D:/web/project/前端10/components/house-card/house-card.vue?6526", "webpack:///D:/web/project/前端10/components/house-card/house-card.vue?8159", "webpack:///D:/web/project/前端10/components/house-card/house-card.vue?1c26", "webpack:///D:/web/project/前端10/components/house-card/house-card.vue?d89f", "uni-app:///components/house-card/house-card.vue", "webpack:///D:/web/project/前端10/components/house-card/house-card.vue?b93b", "webpack:///D:/web/project/前端10/components/house-card/house-card.vue?7d28"], "names": ["name", "props", "houseData", "type", "required", "default", "_id", "title", "description", "price", "address", "images", "tags", "is_favorite", "showFavorite", "methods", "onCardClick", "onFavoriteClick"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAmI;AACnI;AAC8D;AACL;AACsC;;;AAG/F;AACyK;AACzK,gBAAgB,gLAAU;AAC1B,EAAE,gFAAM;AACR,EAAE,iGAAM;AACR,EAAE,0GAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,qGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACnBA;AAAA;AAAA;AAAA;AAAqoB,CAAgB,moBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;gBC+BzpB;EACAA;EACAC;IACAC;MACAC;MACAC;MACAC;QAAA;UACAC;UACAC;UACAC;UACAC;UACAC;UACAC;UACAC;UACAC;QACA;MAAA;IACA;IACAC;MACAX;MACAE;IACA;EACA;EAEAU;IACAC;MACA;IACA;IAEAC;MACA;IACA;EACA;AACA;AAAA,4B;;;;;;;;;;;;AC/DA;AAAA;AAAA;AAAA;AAAguC,CAAgB,spCAAG,EAAC,C;;;;;;;;;;;ACApvC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "components/house-card/house-card.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./house-card.vue?vue&type=template&id=529060b8&scoped=true&\"\nvar renderjs\nimport script from \"./house-card.vue?vue&type=script&lang=js&\"\nexport * from \"./house-card.vue?vue&type=script&lang=js&\"\nimport style0 from \"./house-card.vue?vue&type=style&index=0&id=529060b8&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"529060b8\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"components/house-card/house-card.vue\"\nexport default component.exports", "export * from \"-!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./house-card.vue?vue&type=template&id=529060b8&scoped=true&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 = _vm.houseData.tags && _vm.houseData.tags.length > 0\n  var l0 = g0 ? _vm.houseData.tags.slice(0, 3) : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n        l0: l0,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./house-card.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./house-card.vue?vue&type=script&lang=js&\"", "<template>\n  <view class=\"house-card\" @click=\"onCardClick\">\n    <image class=\"house-image\" :src=\"houseData.images[0]\" mode=\"aspectFill\"></image>\n    <view class=\"house-info\">\n      <view class=\"house-title\">{{ houseData.title }}</view>\n      <view class=\"house-desc\">{{ houseData.description }}</view>\n      <view class=\"house-tags\" v-if=\"houseData.tags && houseData.tags.length > 0\">\n        <text class=\"tag\" v-for=\"tag in houseData.tags.slice(0, 3)\" :key=\"tag\">{{ tag }}</text>\n      </view>\n      <view class=\"house-bottom\">\n        <view class=\"price\">\n          <text class=\"price-num\">{{ houseData.price }}</text>\n          <text class=\"price-unit\">元/月</text>\n        </view>\n        <view class=\"location\">\n          <uni-icons type=\"location\" size=\"12\" color=\"#999\"></uni-icons>\n          <text class=\"location-text\">{{ houseData.address }}</text>\n        </view>\n      </view>\n    </view>\n    <view class=\"favorite-btn\" @click.stop=\"onFavoriteClick\" v-if=\"showFavorite\">\n      <uni-icons \n        :type=\"houseData.is_favorite ? 'heart-filled' : 'heart'\" \n        size=\"20\" \n        :color=\"houseData.is_favorite ? '#FF4D4F' : '#999'\"\n      ></uni-icons>\n    </view>\n  </view>\n</template>\n\n<script>\nexport default {\n  name: 'HouseCard',\n  props: {\n    houseData: {\n      type: Object,\n      required: true,\n      default: () => ({\n        _id: '',\n        title: '',\n        description: '',\n        price: 0,\n        address: '',\n        images: [],\n        tags: [],\n        is_favorite: false\n      })\n    },\n    showFavorite: {\n      type: Boolean,\n      default: true\n    }\n  },\n  \n  methods: {\n    onCardClick() {\n      this.$emit('click', this.houseData)\n    },\n    \n    onFavoriteClick() {\n      this.$emit('favorite', this.houseData)\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.house-card {\n  position: relative;\n  display: flex;\n  background-color: #fff;\n  border-radius: $border-radius-large;\n  overflow: hidden;\n  box-shadow: $box-shadow-light;\n  \n  .house-image {\n    width: 240rpx;\n    height: 180rpx;\n    flex-shrink: 0;\n  }\n  \n  .house-info {\n    flex: 1;\n    padding: $spacing-md;\n    display: flex;\n    flex-direction: column;\n    justify-content: space-between;\n    \n    .house-title {\n      font-size: 30rpx;\n      font-weight: 600;\n      color: $text-color-primary;\n      margin-bottom: $spacing-xs;\n      overflow: hidden;\n      text-overflow: ellipsis;\n      white-space: nowrap;\n      padding-right: 60rpx;\n    }\n    \n    .house-desc {\n      font-size: 24rpx;\n      color: $text-color-tertiary;\n      margin-bottom: $spacing-xs;\n      overflow: hidden;\n      text-overflow: ellipsis;\n      white-space: nowrap;\n    }\n    \n    .house-tags {\n      margin-bottom: $spacing-sm;\n      \n      .tag {\n        display: inline-block;\n        padding: 4rpx 12rpx;\n        background-color: $bg-color-light;\n        color: $text-color-secondary;\n        font-size: 20rpx;\n        border-radius: 8rpx;\n        margin-right: $spacing-xs;\n      }\n    }\n    \n    .house-bottom {\n      display: flex;\n      justify-content: space-between;\n      align-items: flex-end;\n      \n      .price {\n        .price-num {\n          font-size: 32rpx;\n          font-weight: 600;\n          color: $error-color;\n        }\n        \n        .price-unit {\n          font-size: 24rpx;\n          color: $text-color-tertiary;\n        }\n      }\n      \n      .location {\n        display: flex;\n        align-items: center;\n        \n        .location-text {\n          margin-left: 4rpx;\n          font-size: 22rpx;\n          color: $text-color-tertiary;\n          overflow: hidden;\n          text-overflow: ellipsis;\n          white-space: nowrap;\n          max-width: 160rpx;\n        }\n      }\n    }\n  }\n  \n  .favorite-btn {\n    position: absolute;\n    top: $spacing-md;\n    right: $spacing-md;\n    width: 60rpx;\n    height: 60rpx;\n    display: flex;\n    align-items: center;\n    justify-content: center;\n    background-color: rgba(255, 255, 255, 0.9);\n    border-radius: 50%;\n  }\n}\n</style>\n", "import mod from \"-!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./house-card.vue?vue&type=style&index=0&id=529060b8&lang=scss&scoped=true&\"; export default mod; export * from \"-!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./house-card.vue?vue&type=style&index=0&id=529060b8&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754033370419\n      var cssReload = require(\"D:/web/wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}
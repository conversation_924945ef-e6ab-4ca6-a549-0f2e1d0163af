<view class="house-detail-page data-v-4265f319"><view class="image-section data-v-4265f319"><swiper class="image-swiper data-v-4265f319" indicator-dots="{{true}}" circular="{{true}}"><block wx:for="{{houseDetail.images}}" wx:for-item="image" wx:for-index="index" wx:key="index"><swiper-item class="data-v-4265f319"><image class="house-image data-v-4265f319" src="{{image}}" mode="aspectFill" data-event-opts="{{[['tap',[['previewImage',[index]]]]]}}" bindtap="__e"></image></swiper-item></block></swiper><view class="image-count data-v-4265f319"><text class="data-v-4265f319">{{currentImageIndex+1+"/"+(houseDetail.images?.length||0)}}</text></view></view><view class="info-section data-v-4265f319"><view class="price-info data-v-4265f319"><text class="price data-v-4265f319">{{houseDetail.price}}</text><text class="unit data-v-4265f319">元/月</text><view data-event-opts="{{[['tap',[['toggleFavorite',['$event']]]]]}}" class="favorite-btn data-v-4265f319" bindtap="__e"><uni-icons vue-id="0a58f10d-1" type="{{houseDetail.is_favorite?'heart-filled':'heart'}}" size="24" color="{{houseDetail.is_favorite?'#FF4D4F':'#999'}}" class="data-v-4265f319" bind:__l="__l"></uni-icons></view></view><view class="title data-v-4265f319">{{houseDetail.title}}</view><view class="tags data-v-4265f319"><block wx:for="{{houseDetail.tags}}" wx:for-item="tag" wx:for-index="__i0__" wx:key="*this"><text class="tag data-v-4265f319">{{tag}}</text></block></view><view class="location data-v-4265f319"><uni-icons vue-id="0a58f10d-2" type="location" size="16" color="#999" class="data-v-4265f319" bind:__l="__l"></uni-icons><text class="address data-v-4265f319">{{houseDetail.address}}</text></view></view><view class="config-section data-v-4265f319"><view class="section-title data-v-4265f319">房源配置</view><view class="config-grid data-v-4265f319"><view class="config-item data-v-4265f319"><text class="config-label data-v-4265f319">面积</text><text class="config-value data-v-4265f319">{{houseDetail.area+"㎡"}}</text></view><view class="config-item data-v-4265f319"><text class="config-label data-v-4265f319">楼层</text><text class="config-value data-v-4265f319">{{houseDetail.floor}}</text></view><view class="config-item data-v-4265f319"><text class="config-label data-v-4265f319">朝向</text><text class="config-value data-v-4265f319">{{houseDetail.orientation}}</text></view><view class="config-item data-v-4265f319"><text class="config-label data-v-4265f319">装修</text><text class="config-value data-v-4265f319">{{houseDetail.decoration}}</text></view></view></view><view class="desc-section data-v-4265f319"><view class="section-title data-v-4265f319">房源描述</view><text class="desc-content data-v-4265f319">{{houseDetail.description}}</text></view><view class="owner-section data-v-4265f319"><view class="section-title data-v-4265f319">房东信息</view><view class="owner-info data-v-4265f319"><image class="owner-avatar data-v-4265f319" src="{{houseDetail.owner?.avatar||'/static/default-avatar.png'}}"></image><view class="owner-details data-v-4265f319"><text class="owner-name data-v-4265f319">{{houseDetail.owner?.nickname||'房东'}}</text><text class="owner-desc data-v-4265f319">{{houseDetail.owner?.description||'暂无介绍'}}</text></view><button data-event-opts="{{[['tap',[['contactOwner',['$event']]]]]}}" class="contact-btn data-v-4265f319" bindtap="__e">联系房东</button></view></view><view class="bottom-bar data-v-4265f319"><button data-event-opts="{{[['tap',[['shareHouse',['$event']]]]]}}" class="share-btn data-v-4265f319" bindtap="__e"><uni-icons vue-id="0a58f10d-3" type="redo" size="20" color="#666" class="data-v-4265f319" bind:__l="__l"></uni-icons><text class="data-v-4265f319">分享</text></button><button data-event-opts="{{[['tap',[['toggleFavorite',['$event']]]]]}}" class="favorite-btn data-v-4265f319" bindtap="__e"><uni-icons vue-id="0a58f10d-4" type="{{houseDetail.is_favorite?'heart-filled':'heart'}}" size="20" color="{{houseDetail.is_favorite?'#FF4D4F':'#666'}}" class="data-v-4265f319" bind:__l="__l"></uni-icons><text class="data-v-4265f319">{{houseDetail.is_favorite?'已收藏':'收藏'}}</text></button><button data-event-opts="{{[['tap',[['contactOwner',['$event']]]]]}}" class="contact-btn data-v-4265f319" bindtap="__e">立即联系</button></view><block wx:if="{{loading}}"><view class="loading-state data-v-4265f319"><uni-load-more vue-id="0a58f10d-5" status="loading" class="data-v-4265f319" bind:__l="__l"></uni-load-more></view></block></view>
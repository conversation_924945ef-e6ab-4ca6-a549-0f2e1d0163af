/**
 * 云函数接口测试脚本
 * 用于验证所有云函数接口的功能
 */

'use strict';

exports.main = async (event, context) => {
  console.log('开始执行云函数接口测试...');
  
  const { testType = 'all' } = event;
  const results = [];
  
  try {
    switch (testType) {
      case 'user-auth':
        return await testUserAuth();
      case 'house-manage':
        return await testHouseManage();
      case 'file-upload':
        return await testFileUpload();
      case 'admin-manage':
        return await testAdminManage();
      case 'all':
      default:
        return await testAllFunctions();
    }
  } catch (error) {
    console.error('测试执行错误：', error);
    return {
      success: false,
      message: '测试执行失败',
      error: error.message
    };
  }
};

// 测试所有云函数
async function testAllFunctions() {
  const results = {
    success: true,
    message: '云函数接口测试完成',
    tests: []
  };
  
  // 测试用户认证
  const userAuthResult = await testUserAuth();
  results.tests.push({
    name: 'user-auth',
    ...userAuthResult
  });
  
  // 测试房源管理
  const houseManageResult = await testHouseManage();
  results.tests.push({
    name: 'house-manage',
    ...houseManageResult
  });
  
  // 测试文件上传
  const fileUploadResult = await testFileUpload();
  results.tests.push({
    name: 'file-upload',
    ...fileUploadResult
  });
  
  // 测试管理员功能
  const adminManageResult = await testAdminManage();
  results.tests.push({
    name: 'admin-manage',
    ...adminManageResult
  });
  
  // 检查是否有失败的测试
  const hasFailure = results.tests.some(test => !test.success);
  if (hasFailure) {
    results.success = false;
    results.message = '部分测试失败，请检查详细信息';
  }
  
  return results;
}

// 测试用户认证云函数
async function testUserAuth() {
  const tests = [];
  
  try {
    // 测试获取用户信息（无token）
    const getUserInfoResult = await uniCloud.callFunction({
      name: 'user-auth',
      data: {
        action: 'getUserInfo'
      }
    });
    
    tests.push({
      name: 'getUserInfo (no token)',
      success: getUserInfoResult.result.code === 1002,
      expected: '应该返回用户未登录错误',
      actual: `code: ${getUserInfoResult.result.code}, message: ${getUserInfoResult.result.message}`
    });
    
    // 测试更新用户信息（无token）
    const updateUserInfoResult = await uniCloud.callFunction({
      name: 'user-auth',
      data: {
        action: 'updateUserInfo',
        nickname: '测试用户'
      }
    });
    
    tests.push({
      name: 'updateUserInfo (no token)',
      success: updateUserInfoResult.result.code === 1002,
      expected: '应该返回用户未登录错误',
      actual: `code: ${updateUserInfoResult.result.code}, message: ${updateUserInfoResult.result.message}`
    });
    
    // 测试不支持的操作
    const invalidActionResult = await uniCloud.callFunction({
      name: 'user-auth',
      data: {
        action: 'invalidAction'
      }
    });
    
    tests.push({
      name: 'invalid action',
      success: invalidActionResult.result.code === 1001,
      expected: '应该返回不支持的操作类型错误',
      actual: `code: ${invalidActionResult.result.code}, message: ${invalidActionResult.result.message}`
    });
    
  } catch (error) {
    tests.push({
      name: 'user-auth error',
      success: false,
      expected: '正常执行',
      actual: `执行错误: ${error.message}`
    });
  }
  
  const successCount = tests.filter(test => test.success).length;
  
  return {
    success: successCount === tests.length,
    message: `用户认证测试完成，${successCount}/${tests.length} 通过`,
    details: tests
  };
}

// 测试房源管理云函数
async function testHouseManage() {
  const tests = [];
  
  try {
    // 测试获取房源列表
    const getHouseListResult = await uniCloud.callFunction({
      name: 'house-manage',
      data: {
        action: 'getHouseList',
        page: 1,
        limit: 10
      }
    });
    
    tests.push({
      name: 'getHouseList',
      success: getHouseListResult.result.code === 0,
      expected: '应该成功返回房源列表',
      actual: `code: ${getHouseListResult.result.code}, message: ${getHouseListResult.result.message}`
    });
    
    // 测试获取房源详情（不存在的房源）
    const getHouseDetailResult = await uniCloud.callFunction({
      name: 'house-manage',
      data: {
        action: 'getHouseDetail',
        house_id: 'non_existent_id'
      }
    });
    
    tests.push({
      name: 'getHouseDetail (non-existent)',
      success: getHouseDetailResult.result.code === 3001,
      expected: '应该返回房源不存在错误',
      actual: `code: ${getHouseDetailResult.result.code}, message: ${getHouseDetailResult.result.message}`
    });
    
    // 测试发布房源（无token）
    const publishHouseResult = await uniCloud.callFunction({
      name: 'house-manage',
      data: {
        action: 'publishHouse',
        title: '测试房源',
        desc: '测试描述',
        price: 1000,
        location: { address: '测试地址' },
        contact: { phone: '13800138000' }
      }
    });
    
    tests.push({
      name: 'publishHouse (no token)',
      success: publishHouseResult.result.code === 1002,
      expected: '应该返回用户未登录错误',
      actual: `code: ${publishHouseResult.result.code}, message: ${publishHouseResult.result.message}`
    });
    
  } catch (error) {
    tests.push({
      name: 'house-manage error',
      success: false,
      expected: '正常执行',
      actual: `执行错误: ${error.message}`
    });
  }
  
  const successCount = tests.filter(test => test.success).length;
  
  return {
    success: successCount === tests.length,
    message: `房源管理测试完成，${successCount}/${tests.length} 通过`,
    details: tests
  };
}

// 测试文件上传云函数
async function testFileUpload() {
  const tests = [];
  
  try {
    // 测试上传图片（无token）
    const uploadImageResult = await uniCloud.callFunction({
      name: 'file-upload',
      data: {
        action: 'uploadImage',
        file: { name: 'test.jpg', type: 'image/jpeg' }
      }
    });
    
    tests.push({
      name: 'uploadImage (no token)',
      success: uploadImageResult.result.code === 1002,
      expected: '应该返回用户未登录错误',
      actual: `code: ${uploadImageResult.result.code}, message: ${uploadImageResult.result.message}`
    });
    
    // 测试获取上传凭证（无token）
    const getUploadTokenResult = await uniCloud.callFunction({
      name: 'file-upload',
      data: {
        action: 'getUploadToken'
      }
    });
    
    tests.push({
      name: 'getUploadToken (no token)',
      success: getUploadTokenResult.result.code === 1002,
      expected: '应该返回用户未登录错误',
      actual: `code: ${getUploadTokenResult.result.code}, message: ${getUploadTokenResult.result.message}`
    });
    
  } catch (error) {
    tests.push({
      name: 'file-upload error',
      success: false,
      expected: '正常执行',
      actual: `执行错误: ${error.message}`
    });
  }
  
  const successCount = tests.filter(test => test.success).length;
  
  return {
    success: successCount === tests.length,
    message: `文件上传测试完成，${successCount}/${tests.length} 通过`,
    details: tests
  };
}

// 测试管理员功能云函数
async function testAdminManage() {
  const tests = [];
  
  try {
    // 测试管理员登录（错误密码）
    const adminLoginResult = await uniCloud.callFunction({
      name: 'admin-manage',
      data: {
        action: 'adminLogin',
        username: 'admin',
        password: 'wrong_password'
      }
    });
    
    tests.push({
      name: 'adminLogin (wrong password)',
      success: adminLoginResult.result.code === 5001,
      expected: '应该返回用户名或密码错误',
      actual: `code: ${adminLoginResult.result.code}, message: ${adminLoginResult.result.message}`
    });
    
    // 测试获取房源列表（无token）
    const getHouseListResult = await uniCloud.callFunction({
      name: 'admin-manage',
      data: {
        action: 'getHouseList'
      }
    });
    
    tests.push({
      name: 'getHouseList (no admin token)',
      success: getHouseListResult.result.code === 1002,
      expected: '应该返回缺少token错误',
      actual: `code: ${getHouseListResult.result.code}, message: ${getHouseListResult.result.message}`
    });
    
    // 测试获取系统配置
    const getSystemConfigResult = await uniCloud.callFunction({
      name: 'admin-manage',
      data: {
        action: 'getSystemConfig'
      }
    });
    
    tests.push({
      name: 'getSystemConfig',
      success: getSystemConfigResult.result.code === 0,
      expected: '应该成功返回系统配置',
      actual: `code: ${getSystemConfigResult.result.code}, message: ${getSystemConfigResult.result.message}`
    });
    
  } catch (error) {
    tests.push({
      name: 'admin-manage error',
      success: false,
      expected: '正常执行',
      actual: `执行错误: ${error.message}`
    });
  }
  
  const successCount = tests.filter(test => test.success).length;
  
  return {
    success: successCount === tests.length,
    message: `管理员功能测试完成，${successCount}/${tests.length} 通过`,
    details: tests
  };
}

{"bsonType": "object", "required": ["key", "value"], "properties": {"_id": {"description": "配置ID"}, "key": {"bsonType": "string", "title": "配置键名", "description": "系统配置的键名，唯一标识", "maxLength": 50}, "value": {"bsonType": "string", "title": "配置值", "description": "系统配置的值"}, "desc": {"bsonType": "string", "title": "配置描述", "description": "配置项的说明描述", "maxLength": 200}, "updated_at": {"bsonType": "date", "title": "更新时间"}}, "permission": {"read": true, "create": false, "update": false, "delete": false}}
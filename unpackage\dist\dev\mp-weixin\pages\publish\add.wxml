<view class="publish-page data-v-333d9b38"><form data-event-opts="{{[['submit',[['submitForm',['$event']]]]]}}" bindsubmit="__e" class="data-v-333d9b38"><view class="form-section data-v-333d9b38"><view class="section-title data-v-333d9b38">房源图片<text class="required data-v-333d9b38">*</text></view><view class="image-upload data-v-333d9b38"><view class="image-list data-v-333d9b38"><block wx:for="{{formData.images}}" wx:for-item="image" wx:for-index="index" wx:key="index"><view class="image-item data-v-333d9b38"><image class="uploaded-image data-v-333d9b38" src="{{image}}" mode="aspectFill"></image><view data-event-opts="{{[['tap',[['deleteImage',[index]]]]]}}" class="delete-btn data-v-333d9b38" bindtap="__e"><uni-icons vue-id="{{'34764cce-1-'+index}}" type="close" size="16" color="#fff" class="data-v-333d9b38" bind:__l="__l"></uni-icons></view></view></block><block wx:if="{{$root.g0<9}}"><view data-event-opts="{{[['tap',[['chooseImage',['$event']]]]]}}" class="upload-btn data-v-333d9b38" bindtap="__e"><uni-icons vue-id="34764cce-2" type="camera" size="32" color="#999" class="data-v-333d9b38" bind:__l="__l"></uni-icons><text class="upload-text data-v-333d9b38">添加图片</text><text class="upload-count data-v-333d9b38">{{$root.g1+"/9"}}</text></view></block></view></view></view><view class="form-section data-v-333d9b38"><view class="section-title data-v-333d9b38">基本信息</view><view class="form-item data-v-333d9b38"><text class="label data-v-333d9b38">房源标题<text class="required data-v-333d9b38">*</text></text><input class="input data-v-333d9b38" placeholder="请输入房源标题" maxlength="50" data-event-opts="{{[['input',[['__set_model',['$0','title','$event',[]],['formData']]]]]}}" value="{{formData.title}}" bindinput="__e"/></view><view class="form-item data-v-333d9b38"><text class="label data-v-333d9b38">房源类型<text class="required data-v-333d9b38">*</text></text><picker mode="selector" range="{{typeOptions}}" range-key="label" data-event-opts="{{[['change',[['onTypeChange',['$event']]]]]}}" bindchange="__e" class="data-v-333d9b38"><view class="picker data-v-333d9b38">{{''+(selectedType||'请选择房源类型')+''}}<uni-icons vue-id="34764cce-3" type="right" size="14" color="#999" class="data-v-333d9b38" bind:__l="__l"></uni-icons></view></picker></view><view class="form-item data-v-333d9b38"><text class="label data-v-333d9b38">租金<text class="required data-v-333d9b38">*</text></text><view class="price-input data-v-333d9b38"><input class="input data-v-333d9b38" placeholder="请输入租金" type="number" data-event-opts="{{[['input',[['__set_model',['$0','price','$event',[]],['formData']]]]]}}" value="{{formData.price}}" bindinput="__e"/><text class="unit data-v-333d9b38">元/月</text></view></view><view class="form-item data-v-333d9b38"><text class="label data-v-333d9b38">房源地址<text class="required data-v-333d9b38">*</text></text><view data-event-opts="{{[['tap',[['chooseLocation',['$event']]]]]}}" class="address-input data-v-333d9b38" bindtap="__e"><text class="{{['address-text','data-v-333d9b38',(!formData.address)?'placeholder':'']}}">{{''+(formData.address||'请选择房源地址')+''}}</text><uni-icons vue-id="34764cce-4" type="right" size="14" color="#999" class="data-v-333d9b38" bind:__l="__l"></uni-icons></view></view></view><view class="form-section data-v-333d9b38"><view class="section-title data-v-333d9b38">房源配置</view><view class="form-item data-v-333d9b38"><text class="label data-v-333d9b38">面积</text><view class="area-input data-v-333d9b38"><input class="input data-v-333d9b38" placeholder="请输入面积" type="number" data-event-opts="{{[['input',[['__set_model',['$0','area','$event',[]],['formData']]]]]}}" value="{{formData.area}}" bindinput="__e"/><text class="unit data-v-333d9b38">㎡</text></view></view><view class="form-item data-v-333d9b38"><text class="label data-v-333d9b38">楼层</text><input class="input data-v-333d9b38" placeholder="如：5/10层" data-event-opts="{{[['input',[['__set_model',['$0','floor','$event',[]],['formData']]]]]}}" value="{{formData.floor}}" bindinput="__e"/></view><view class="form-item data-v-333d9b38"><text class="label data-v-333d9b38">朝向</text><picker mode="selector" range="{{orientationOptions}}" data-event-opts="{{[['change',[['onOrientationChange',['$event']]]]]}}" bindchange="__e" class="data-v-333d9b38"><view class="picker data-v-333d9b38">{{''+(formData.orientation||'请选择朝向')+''}}<uni-icons vue-id="34764cce-5" type="right" size="14" color="#999" class="data-v-333d9b38" bind:__l="__l"></uni-icons></view></picker></view><view class="form-item data-v-333d9b38"><text class="label data-v-333d9b38">装修</text><picker mode="selector" range="{{decorationOptions}}" data-event-opts="{{[['change',[['onDecorationChange',['$event']]]]]}}" bindchange="__e" class="data-v-333d9b38"><view class="picker data-v-333d9b38">{{''+(formData.decoration||'请选择装修情况')+''}}<uni-icons vue-id="34764cce-6" type="right" size="14" color="#999" class="data-v-333d9b38" bind:__l="__l"></uni-icons></view></picker></view></view><view class="form-section data-v-333d9b38"><view class="section-title data-v-333d9b38">房源描述</view><textarea class="textarea data-v-333d9b38" placeholder="请详细描述房源情况，如周边配套、交通等" maxlength="500" data-event-opts="{{[['input',[['__set_model',['$0','description','$event',[]],['formData']]]]]}}" value="{{formData.description}}" bindinput="__e"></textarea><view class="char-count data-v-333d9b38">{{$root.g2+"/500"}}</view></view><view class="form-section data-v-333d9b38"><view class="section-title data-v-333d9b38">房源标签</view><view class="tag-list data-v-333d9b38"><block wx:for="{{$root.l0}}" wx:for-item="tag" wx:for-index="__i0__" wx:key="$orig"><view data-event-opts="{{[['tap',[['toggleTag',['$0'],[[['tagOptions','',__i0__]]]]]]]}}" class="{{['tag-item','data-v-333d9b38',(tag.g3)?'active':'']}}" bindtap="__e">{{''+tag.$orig+''}}</view></block></view></view><view class="form-section data-v-333d9b38"><view class="section-title data-v-333d9b38">联系方式</view><view class="form-item data-v-333d9b38"><text class="label data-v-333d9b38">联系电话</text><input class="input data-v-333d9b38" placeholder="请输入联系电话" type="number" data-event-opts="{{[['input',[['__set_model',['$0','contact_phone','$event',[]],['formData']]]]]}}" value="{{formData.contact_phone}}" bindinput="__e"/></view></view><view class="submit-section data-v-333d9b38"><button class="submit-btn data-v-333d9b38" disabled="{{submitting}}" data-event-opts="{{[['tap',[['submitForm',['$event']]]]]}}" bindtap="__e">{{''+(submitting?'发布中...':'发布房源')+''}}</button></view></form></view>
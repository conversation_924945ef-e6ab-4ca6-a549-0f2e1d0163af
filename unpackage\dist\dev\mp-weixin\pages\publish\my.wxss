@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 自定义项目样式变量 */
/* 主题色 */
/* 辅助色 */
/* 中性色 */
/* 背景色 */
/* 边框色 */
/* 阴影 */
/* 圆角 */
/* 间距 */
.my-publish-page.data-v-b8d65436 {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: #F5F5F5;
  position: relative;
}
/* 状态筛选 */
.status-tabs.data-v-b8d65436 {
  display: flex;
  background-color: #fff;
  border-bottom: 1px solid #E8E8E8;
}
.status-tabs .tab-item.data-v-b8d65436 {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  height: 88rpx;
  position: relative;
}
.status-tabs .tab-item .tab-text.data-v-b8d65436 {
  font-size: 28rpx;
  color: #595959;
}
.status-tabs .tab-item .tab-count.data-v-b8d65436 {
  margin-left: 8rpx;
  padding: 2rpx 8rpx;
  background-color: #FF4D4F;
  color: #fff;
  font-size: 20rpx;
  border-radius: 10rpx;
  min-width: 32rpx;
  text-align: center;
}
.status-tabs .tab-item.active .tab-text.data-v-b8d65436 {
  color: #007AFF;
  font-weight: 600;
}
.status-tabs .tab-item.active.data-v-b8d65436::after {
  content: "";
  position: absolute;
  bottom: 0;
  left: 50%;
  -webkit-transform: translateX(-50%);
          transform: translateX(-50%);
  width: 60rpx;
  height: 4rpx;
  background-color: #007AFF;
  border-radius: 2rpx;
}
/* 房源列表 */
.house-scroll.data-v-b8d65436 {
  flex: 1;
}
.house-list.data-v-b8d65436 {
  padding: 24rpx;
}
.house-list .house-item.data-v-b8d65436 {
  display: flex;
  background-color: #fff;
  border-radius: 12rpx;
  margin-bottom: 24rpx;
  overflow: hidden;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}
.house-list .house-item .house-image.data-v-b8d65436 {
  width: 200rpx;
  height: 150rpx;
  flex-shrink: 0;
}
.house-list .house-item .house-info.data-v-b8d65436 {
  flex: 1;
  padding: 24rpx;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}
.house-list .house-item .house-info .house-title.data-v-b8d65436 {
  font-size: 28rpx;
  font-weight: 600;
  color: #262626;
  margin-bottom: 8rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.house-list .house-item .house-info .house-desc.data-v-b8d65436 {
  font-size: 24rpx;
  color: #8C8C8C;
  margin-bottom: 16rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.house-list .house-item .house-info .house-meta.data-v-b8d65436 {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16rpx;
}
.house-list .house-item .house-info .house-meta .price.data-v-b8d65436 {
  font-size: 28rpx;
  font-weight: 600;
  color: #FF4D4F;
}
.house-list .house-item .house-info .house-meta .status.data-v-b8d65436 {
  padding: 4rpx 12rpx;
  font-size: 20rpx;
  border-radius: 8rpx;
}
.house-list .house-item .house-info .house-meta .status.status-pending.data-v-b8d65436 {
  background-color: rgba(250, 173, 20, 0.1);
  color: #FAAD14;
}
.house-list .house-item .house-info .house-meta .status.status-approved.data-v-b8d65436 {
  background-color: rgba(82, 196, 26, 0.1);
  color: #52C41A;
}
.house-list .house-item .house-info .house-meta .status.status-rejected.data-v-b8d65436 {
  background-color: rgba(255, 77, 79, 0.1);
  color: #FF4D4F;
}
.house-list .house-item .house-info .house-stats.data-v-b8d65436 {
  display: flex;
  align-items: center;
}
.house-list .house-item .house-info .house-stats .stat-item.data-v-b8d65436 {
  font-size: 22rpx;
  color: #8C8C8C;
  margin-right: 24rpx;
}
.house-list .house-item .house-info .house-stats .publish-time.data-v-b8d65436 {
  margin-left: auto;
  font-size: 22rpx;
  color: #BFBFBF;
}
.house-list .house-item .action-btns.data-v-b8d65436 {
  display: flex;
  flex-direction: column;
  justify-content: center;
  padding: 24rpx;
  gap: 16rpx;
}
.house-list .house-item .action-btns .action-btn.data-v-b8d65436 {
  width: 120rpx;
  height: 60rpx;
  font-size: 24rpx;
  border: none;
  border-radius: 8rpx;
}
.house-list .house-item .action-btns .action-btn.view-btn.data-v-b8d65436 {
  background-color: #007AFF;
  color: #fff;
}
.house-list .house-item .action-btns .action-btn.edit-btn.data-v-b8d65436 {
  background-color: #FAAD14;
  color: #fff;
}
.house-list .house-item .action-btns .action-btn.delete-btn.data-v-b8d65436 {
  background-color: #FAFAFA;
  color: #FF4D4F;
}
/* 加载状态 */
.loading-more.data-v-b8d65436 {
  padding: 32rpx;
}
/* 空状态 */
.empty-state.data-v-b8d65436 {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 120rpx 32rpx;
}
.empty-state .empty-image.data-v-b8d65436 {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 32rpx;
}
.empty-state .empty-text.data-v-b8d65436 {
  font-size: 28rpx;
  color: #8C8C8C;
  margin-bottom: 32rpx;
}
.empty-state .publish-btn.data-v-b8d65436 {
  width: 200rpx;
  height: 72rpx;
  background-color: #007AFF;
  color: #fff;
  font-size: 26rpx;
  border: none;
  border-radius: 8rpx;
}
/* 悬浮发布按钮 */
.float-publish-btn.data-v-b8d65436 {
  position: fixed;
  bottom: 120rpx;
  right: 32rpx;
  width: 100rpx;
  height: 100rpx;
  background-color: #007AFF;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  z-index: 100;
}

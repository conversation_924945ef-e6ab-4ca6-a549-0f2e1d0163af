@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 自定义项目样式变量 */
/* 主题色 */
/* 辅助色 */
/* 中性色 */
/* 背景色 */
/* 边框色 */
/* 阴影 */
/* 圆角 */
/* 间距 */
.publish-page.data-v-333d9b38 {
  background-color: #F5F5F5;
  padding-bottom: 48rpx;
}
/* 表单区块 */
.form-section.data-v-333d9b38 {
  background-color: #fff;
  margin-bottom: 24rpx;
  padding: 32rpx;
}
.form-section .section-title.data-v-333d9b38 {
  font-size: 32rpx;
  font-weight: 600;
  color: #262626;
  margin-bottom: 32rpx;
}
.form-section .section-title .required.data-v-333d9b38 {
  color: #FF4D4F;
}
/* 图片上传 */
.image-upload .image-list.data-v-333d9b38 {
  display: flex;
  flex-wrap: wrap;
  gap: 24rpx;
}
.image-upload .image-list .image-item.data-v-333d9b38 {
  position: relative;
  width: 200rpx;
  height: 200rpx;
  border-radius: 8rpx;
  overflow: hidden;
}
.image-upload .image-list .image-item .uploaded-image.data-v-333d9b38 {
  width: 100%;
  height: 100%;
}
.image-upload .image-list .image-item .delete-btn.data-v-333d9b38 {
  position: absolute;
  top: 8rpx;
  right: 8rpx;
  width: 40rpx;
  height: 40rpx;
  background-color: rgba(0, 0, 0, 0.5);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}
.image-upload .image-list .upload-btn.data-v-333d9b38 {
  width: 200rpx;
  height: 200rpx;
  border: 2rpx dashed #D9D9D9;
  border-radius: 8rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background-color: #FAFAFA;
}
.image-upload .image-list .upload-btn .upload-text.data-v-333d9b38 {
  font-size: 24rpx;
  color: #8C8C8C;
  margin-top: 8rpx;
}
.image-upload .image-list .upload-btn .upload-count.data-v-333d9b38 {
  font-size: 20rpx;
  color: #BFBFBF;
  margin-top: 4rpx;
}
/* 表单项 */
.form-item.data-v-333d9b38 {
  margin-bottom: 32rpx;
}
.form-item.data-v-333d9b38:last-child {
  margin-bottom: 0;
}
.form-item .label.data-v-333d9b38 {
  display: block;
  font-size: 28rpx;
  color: #262626;
  margin-bottom: 16rpx;
}
.form-item .label .required.data-v-333d9b38 {
  color: #FF4D4F;
}
.form-item .input.data-v-333d9b38 {
  width: 100%;
  height: 80rpx;
  padding: 0 24rpx;
  background-color: #FAFAFA;
  border: 1px solid #E8E8E8;
  border-radius: 8rpx;
  font-size: 28rpx;
  color: #262626;
}
.form-item .input.data-v-333d9b38::-webkit-input-placeholder {
  color: #BFBFBF;
}
.form-item .input.data-v-333d9b38::placeholder {
  color: #BFBFBF;
}
.form-item .picker.data-v-333d9b38 {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 80rpx;
  padding: 0 24rpx;
  background-color: #FAFAFA;
  border: 1px solid #E8E8E8;
  border-radius: 8rpx;
  font-size: 28rpx;
  color: #262626;
}
.form-item .price-input.data-v-333d9b38,
.form-item .area-input.data-v-333d9b38 {
  display: flex;
  align-items: center;
}
.form-item .price-input .input.data-v-333d9b38,
.form-item .area-input .input.data-v-333d9b38 {
  flex: 1;
  margin-right: 16rpx;
}
.form-item .price-input .unit.data-v-333d9b38,
.form-item .area-input .unit.data-v-333d9b38 {
  font-size: 28rpx;
  color: #595959;
}
.form-item .address-input.data-v-333d9b38 {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 80rpx;
  padding: 0 24rpx;
  background-color: #FAFAFA;
  border: 1px solid #E8E8E8;
  border-radius: 8rpx;
}
.form-item .address-input .address-text.data-v-333d9b38 {
  font-size: 28rpx;
  color: #262626;
}
.form-item .address-input .address-text.placeholder.data-v-333d9b38 {
  color: #BFBFBF;
}
/* 文本域 */
.textarea.data-v-333d9b38 {
  width: 100%;
  min-height: 200rpx;
  padding: 24rpx;
  background-color: #FAFAFA;
  border: 1px solid #E8E8E8;
  border-radius: 8rpx;
  font-size: 28rpx;
  color: #262626;
  line-height: 1.5;
}
.textarea.data-v-333d9b38::-webkit-input-placeholder {
  color: #BFBFBF;
}
.textarea.data-v-333d9b38::placeholder {
  color: #BFBFBF;
}
.char-count.data-v-333d9b38 {
  text-align: right;
  font-size: 24rpx;
  color: #BFBFBF;
  margin-top: 8rpx;
}
/* 标签列表 */
.tag-list.data-v-333d9b38 {
  display: flex;
  flex-wrap: wrap;
  gap: 24rpx;
}
.tag-list .tag-item.data-v-333d9b38 {
  padding: 16rpx 24rpx;
  background-color: #FAFAFA;
  color: #595959;
  font-size: 26rpx;
  border-radius: 8rpx;
  border: 1px solid transparent;
}
.tag-list .tag-item.active.data-v-333d9b38 {
  background-color: rgba(0, 122, 255, 0.1);
  color: #007AFF;
  border-color: #007AFF;
}
/* 提交区域 */
.submit-section.data-v-333d9b38 {
  padding: 32rpx;
}
.submit-section .submit-btn.data-v-333d9b38 {
  width: 100%;
  height: 88rpx;
  background-color: #007AFF;
  color: #fff;
  font-size: 32rpx;
  border: none;
  border-radius: 8rpx;
}
.submit-section .submit-btn.data-v-333d9b38:disabled {
  background-color: #BFBFBF;
}

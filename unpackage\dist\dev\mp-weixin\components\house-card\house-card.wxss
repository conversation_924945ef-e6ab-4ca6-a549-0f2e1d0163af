@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 自定义项目样式变量 */
/* 主题色 */
/* 辅助色 */
/* 中性色 */
/* 背景色 */
/* 边框色 */
/* 阴影 */
/* 圆角 */
/* 间距 */
.house-card.data-v-529060b8 {
  position: relative;
  display: flex;
  background-color: #fff;
  border-radius: 12rpx;
  overflow: hidden;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}
.house-card .house-image.data-v-529060b8 {
  width: 240rpx;
  height: 180rpx;
  flex-shrink: 0;
}
.house-card .house-info.data-v-529060b8 {
  flex: 1;
  padding: 24rpx;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}
.house-card .house-info .house-title.data-v-529060b8 {
  font-size: 30rpx;
  font-weight: 600;
  color: #262626;
  margin-bottom: 8rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  padding-right: 60rpx;
}
.house-card .house-info .house-desc.data-v-529060b8 {
  font-size: 24rpx;
  color: #8C8C8C;
  margin-bottom: 8rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.house-card .house-info .house-tags.data-v-529060b8 {
  margin-bottom: 16rpx;
}
.house-card .house-info .house-tags .tag.data-v-529060b8 {
  display: inline-block;
  padding: 4rpx 12rpx;
  background-color: #FAFAFA;
  color: #595959;
  font-size: 20rpx;
  border-radius: 8rpx;
  margin-right: 8rpx;
}
.house-card .house-info .house-bottom.data-v-529060b8 {
  display: flex;
  justify-content: space-between;
  align-items: flex-end;
}
.house-card .house-info .house-bottom .price .price-num.data-v-529060b8 {
  font-size: 32rpx;
  font-weight: 600;
  color: #FF4D4F;
}
.house-card .house-info .house-bottom .price .price-unit.data-v-529060b8 {
  font-size: 24rpx;
  color: #8C8C8C;
}
.house-card .house-info .house-bottom .location.data-v-529060b8 {
  display: flex;
  align-items: center;
}
.house-card .house-info .house-bottom .location .location-text.data-v-529060b8 {
  margin-left: 4rpx;
  font-size: 22rpx;
  color: #8C8C8C;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  max-width: 160rpx;
}
.house-card .favorite-btn.data-v-529060b8 {
  position: absolute;
  top: 24rpx;
  right: 24rpx;
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: rgba(255, 255, 255, 0.9);
  border-radius: 50%;
}

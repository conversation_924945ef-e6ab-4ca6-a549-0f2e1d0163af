<template>
  <view class="login-page">
    <!-- 顶部装饰 -->
    <view class="header-section">
      <image class="logo" src="/static/logo.png"></image>
      <text class="app-name">找房租房</text>
      <text class="slogan">让租房变得更简单</text>
    </view>

    <!-- 登录表单 -->
    <view class="login-section">
      <view class="login-title">欢迎使用</view>
      <view class="login-desc">请使用微信授权登录</view>
      
      <!-- 微信登录按钮 -->
      <button 
        class="wechat-login-btn" 
        open-type="getUserInfo"
        @getuserinfo="onGetUserInfo"
        :disabled="logging"
      >
        <uni-icons type="weixin" size="24" color="#fff"></uni-icons>
        <text class="btn-text">{{ logging ? '登录中...' : '微信授权登录' }}</text>
      </button>
      
      <!-- 手机号登录 -->
      <view class="phone-login-section">
        <view class="divider">
          <text class="divider-text">或</text>
        </view>
        
        <view class="phone-input">
          <input 
            class="phone-field" 
            v-model="phoneNumber" 
            placeholder="请输入手机号"
            type="number"
            maxlength="11"
          />
        </view>
        
        <view class="code-input">
          <input 
            class="code-field" 
            v-model="verifyCode" 
            placeholder="请输入验证码"
            type="number"
            maxlength="6"
          />
          <button 
            class="send-code-btn" 
            @click="sendCode"
            :disabled="codeSending || countdown > 0"
          >
            {{ countdown > 0 ? `${countdown}s` : '发送验证码' }}
          </button>
        </view>
        
        <button 
          class="phone-login-btn" 
          @click="phoneLogin"
          :disabled="!canPhoneLogin || logging"
        >
          手机号登录
        </button>
      </view>
    </view>

    <!-- 协议条款 -->
    <view class="agreement-section">
      <view class="agreement-text">
        <text>登录即表示同意</text>
        <text class="link" @click="showUserAgreement">《用户协议》</text>
        <text>和</text>
        <text class="link" @click="showPrivacyPolicy">《隐私政策》</text>
      </view>
    </view>

    <!-- 底部装饰 -->
    <view class="footer-section">
      <text class="footer-text">安全 · 便捷 · 可靠</text>
    </view>
  </view>
</template>

<script>
import { authAPI } from '@/utils/request.js'

export default {
  data() {
    return {
      logging: false,
      codeSending: false,
      countdown: 0,
      phoneNumber: '',
      verifyCode: '',
      timer: null
    }
  },
  
  computed: {
    canPhoneLogin() {
      return this.phoneNumber.length === 11 && this.verifyCode.length === 6
    }
  },
  
  onUnload() {
    if (this.timer) {
      clearInterval(this.timer)
    }
  },
  
  methods: {
    // 微信授权登录
    async onGetUserInfo(e) {
      if (e.detail.errMsg !== 'getUserInfo:ok') {
        uni.showToast({
          title: '授权失败',
          icon: 'none'
        })
        return
      }
      
      try {
        this.logging = true
        
        // 获取微信登录code
        const loginRes = await this.getWechatCode()
        
        // 调用登录接口
        const result = await authAPI.login(loginRes.code)
        
        // 保存登录信息
        uni.setStorageSync('token', result.data.token)
        uni.setStorageSync('userInfo', result.data.userInfo)
        
        uni.showToast({
          title: '登录成功',
          icon: 'success'
        })
        
        // 返回上一页或跳转到首页
        setTimeout(() => {
          const pages = getCurrentPages()
          if (pages.length > 1) {
            uni.navigateBack()
          } else {
            uni.switchTab({
              url: '/pages/index/index'
            })
          }
        }, 1500)
        
      } catch (error) {
        console.error('微信登录失败：', error)
        uni.showToast({
          title: error.message || '登录失败',
          icon: 'none'
        })
      } finally {
        this.logging = false
      }
    },
    
    // 获取微信登录code
    getWechatCode() {
      return new Promise((resolve, reject) => {
        uni.login({
          provider: 'weixin',
          success: resolve,
          fail: reject
        })
      })
    },
    
    // 发送验证码
    async sendCode() {
      if (!this.phoneNumber) {
        uni.showToast({
          title: '请输入手机号',
          icon: 'none'
        })
        return
      }
      
      if (!/^1[3-9]\d{9}$/.test(this.phoneNumber)) {
        uni.showToast({
          title: '手机号格式不正确',
          icon: 'none'
        })
        return
      }
      
      try {
        this.codeSending = true
        
        // 调用发送验证码接口
        // await authAPI.sendSmsCode(this.phoneNumber)
        
        // 模拟发送成功
        uni.showToast({
          title: '验证码已发送',
          icon: 'success'
        })
        
        // 开始倒计时
        this.startCountdown()
        
      } catch (error) {
        console.error('发送验证码失败：', error)
        uni.showToast({
          title: error.message || '发送失败',
          icon: 'none'
        })
      } finally {
        this.codeSending = false
      }
    },
    
    // 开始倒计时
    startCountdown() {
      this.countdown = 60
      this.timer = setInterval(() => {
        this.countdown--
        if (this.countdown <= 0) {
          clearInterval(this.timer)
          this.timer = null
        }
      }, 1000)
    },
    
    // 手机号登录
    async phoneLogin() {
      try {
        this.logging = true
        
        // 调用手机号登录接口
        // const result = await authAPI.phoneLogin(this.phoneNumber, this.verifyCode)
        
        // 模拟登录成功
        const mockResult = {
          data: {
            token: 'mock_token_' + Date.now(),
            userInfo: {
              _id: 'mock_user_id',
              nickname: this.phoneNumber.replace(/(\d{3})\d{4}(\d{4})/, '$1****$2'),
              avatar: '',
              phone: this.phoneNumber
            }
          }
        }
        
        // 保存登录信息
        uni.setStorageSync('token', mockResult.data.token)
        uni.setStorageSync('userInfo', mockResult.data.userInfo)
        
        uni.showToast({
          title: '登录成功',
          icon: 'success'
        })
        
        // 返回上一页或跳转到首页
        setTimeout(() => {
          const pages = getCurrentPages()
          if (pages.length > 1) {
            uni.navigateBack()
          } else {
            uni.switchTab({
              url: '/pages/index/index'
            })
          }
        }, 1500)
        
      } catch (error) {
        console.error('手机号登录失败：', error)
        uni.showToast({
          title: error.message || '登录失败',
          icon: 'none'
        })
      } finally {
        this.logging = false
      }
    },
    
    // 显示用户协议
    showUserAgreement() {
      uni.navigateTo({
        url: '/pages/profile/agreement?type=user'
      })
    },
    
    // 显示隐私政策
    showPrivacyPolicy() {
      uni.navigateTo({
        url: '/pages/profile/agreement?type=privacy'
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.login-page {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  flex-direction: column;
  padding: $spacing-xl;
}

/* 顶部装饰 */
.header-section {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-top: 120rpx;
  margin-bottom: 80rpx;
  
  .logo {
    width: 120rpx;
    height: 120rpx;
    border-radius: 24rpx;
    margin-bottom: $spacing-lg;
  }
  
  .app-name {
    font-size: 48rpx;
    font-weight: 600;
    color: #fff;
    margin-bottom: $spacing-sm;
  }
  
  .slogan {
    font-size: 28rpx;
    color: rgba(255, 255, 255, 0.8);
  }
}

/* 登录区域 */
.login-section {
  background-color: #fff;
  border-radius: 24rpx;
  padding: $spacing-xl;
  margin-bottom: $spacing-xl;
  
  .login-title {
    font-size: 36rpx;
    font-weight: 600;
    color: $text-color-primary;
    text-align: center;
    margin-bottom: $spacing-sm;
  }
  
  .login-desc {
    font-size: 26rpx;
    color: $text-color-tertiary;
    text-align: center;
    margin-bottom: $spacing-xl;
  }
  
  .wechat-login-btn {
    width: 100%;
    height: 88rpx;
    background-color: #07C160;
    color: #fff;
    font-size: 30rpx;
    border: none;
    border-radius: $border-radius-base;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: $spacing-lg;
    
    .btn-text {
      margin-left: $spacing-sm;
    }
    
    &:disabled {
      background-color: #ccc;
    }
  }
}

/* 手机号登录 */
.phone-login-section {
  .divider {
    display: flex;
    align-items: center;
    margin: $spacing-xl 0;
    
    &::before,
    &::after {
      content: '';
      flex: 1;
      height: 1px;
      background-color: $border-color-light;
    }
    
    .divider-text {
      padding: 0 $spacing-md;
      font-size: 24rpx;
      color: $text-color-tertiary;
    }
  }
  
  .phone-input,
  .code-input {
    margin-bottom: $spacing-lg;
  }
  
  .phone-field,
  .code-field {
    width: 100%;
    height: 80rpx;
    padding: 0 $spacing-md;
    background-color: $bg-color-light;
    border: 1px solid $border-color-light;
    border-radius: $border-radius-base;
    font-size: 28rpx;
    
    &::placeholder {
      color: $text-color-quaternary;
    }
  }
  
  .code-input {
    display: flex;
    gap: $spacing-md;
    
    .code-field {
      flex: 1;
    }
    
    .send-code-btn {
      width: 200rpx;
      height: 80rpx;
      background-color: $primary-color;
      color: #fff;
      font-size: 24rpx;
      border: none;
      border-radius: $border-radius-base;
      
      &:disabled {
        background-color: $text-color-quaternary;
      }
    }
  }
  
  .phone-login-btn {
    width: 100%;
    height: 88rpx;
    background-color: $primary-color;
    color: #fff;
    font-size: 30rpx;
    border: none;
    border-radius: $border-radius-base;
    
    &:disabled {
      background-color: $text-color-quaternary;
    }
  }
}

/* 协议条款 */
.agreement-section {
  .agreement-text {
    text-align: center;
    font-size: 24rpx;
    color: rgba(255, 255, 255, 0.8);
    
    .link {
      color: #fff;
      text-decoration: underline;
    }
  }
}

/* 底部装饰 */
.footer-section {
  flex: 1;
  display: flex;
  align-items: flex-end;
  justify-content: center;
  padding-bottom: $spacing-xl;
  
  .footer-text {
    font-size: 24rpx;
    color: rgba(255, 255, 255, 0.6);
  }
}
</style>

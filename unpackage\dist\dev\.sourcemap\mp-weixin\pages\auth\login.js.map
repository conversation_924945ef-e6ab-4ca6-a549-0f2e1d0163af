{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/web/project/前端10/pages/auth/login.vue?ae3c", "webpack:///D:/web/project/前端10/pages/auth/login.vue?d91f", "webpack:///D:/web/project/前端10/pages/auth/login.vue?d816", "webpack:///D:/web/project/前端10/pages/auth/login.vue?0c1e", "uni-app:///pages/auth/login.vue", "webpack:///D:/web/project/前端10/pages/auth/login.vue?de9f", "webpack:///D:/web/project/前端10/pages/auth/login.vue?3fa6"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "logging", "codeSending", "countdown", "phoneNumber", "verifyCode", "timer", "computed", "canPhoneLogin", "onUnload", "clearInterval", "methods", "onGetUserInfo", "e", "uni", "title", "icon", "loginRes", "authAPI", "result", "setTimeout", "url", "console", "getWechatCode", "provider", "success", "fail", "sendCode", "startCountdown", "phoneLogin", "mockResult", "token", "userInfo", "_id", "nickname", "avatar", "phone", "showUserAgreement", "showPrivacyPolicy"], "mappings": ";;;;;;;;;;;;;AAAA;AAE2D;AAC3D;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,cAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAA8H;AAC9H;AACyD;AACL;AACsC;;;AAG1F;AACyK;AACzK,gBAAgB,gLAAU;AAC1B,EAAE,2EAAM;AACR,EAAE,4FAAM;AACR,EAAE,qGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,gGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACRA;AAAA;AAAA;AAAA;AAAgoB,CAAgB,8nBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;ACsFppB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eAEA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EAEAC;IACAC;MACA;IACA;EACA;EAEAC;IACA;MACAC;IACA;EACA;EAEAC;IACA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA,MACAC;kBAAA;kBAAA;gBAAA;gBACAC;kBACAC;kBACAC;gBACA;gBAAA;cAAA;gBAAA;gBAKA;;gBAEA;gBAAA;gBAAA,OACA;cAAA;gBAAAC;gBAAA;gBAAA,OAGAC;cAAA;gBAAAC;gBAEA;gBACAL;gBACAA;gBAEAA;kBACAC;kBACAC;gBACA;;gBAEA;gBACAI;kBACA;kBACA;oBACAN;kBACA;oBACAA;sBACAO;oBACA;kBACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAGAC;gBACAR;kBACAC;kBACAC;gBACA;cAAA;gBAAA;gBAEA;gBAAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAO;MACA;QACAT;UACAU;UACAC;UACAC;QACA;MACA;IACA;IAEA;IACAC;MAAA;MAAA;QAAA;UAAA;YAAA;cAAA;gBAAA,IACA;kBAAA;kBAAA;gBAAA;gBACAb;kBACAC;kBACAC;gBACA;gBAAA;cAAA;gBAAA,IAIA;kBAAA;kBAAA;gBAAA;gBACAF;kBACAC;kBACAC;gBACA;gBAAA;cAAA;gBAIA;kBACA;;kBAEA;kBACA;;kBAEA;kBACAF;oBACAC;oBACAC;kBACA;;kBAEA;kBACA;gBAEA;kBACAM;kBACAR;oBACAC;oBACAC;kBACA;gBACA;kBACA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IAEA;IACAY;MAAA;MACA;MACA;QACA;QACA;UACAlB;UACA;QACA;MACA;IACA;IAEA;IACAmB;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBACA;kBACA;;kBAEA;kBACA;;kBAEA;kBACAC;oBACA9B;sBACA+B;sBACAC;wBACAC;wBACAC;wBACAC;wBACAC;sBACA;oBACA;kBACA,GAEA;kBACAtB;kBACAA;kBAEAA;oBACAC;oBACAC;kBACA;;kBAEA;kBACAI;oBACA;oBACA;sBACAN;oBACA;sBACAA;wBACAO;sBACA;oBACA;kBACA;gBAEA;kBACAC;kBACAR;oBACAC;oBACAC;kBACA;gBACA;kBACA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IAEA;IACAqB;MACAvB;QACAO;MACA;IACA;IAEA;IACAiB;MACAxB;QACAO;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AC1SA;AAAA;AAAA;AAAA;AAA2tC,CAAgB,ipCAAG,EAAC,C;;;;;;;;;;;ACA/uC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/auth/login.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;import '@dcloudio/vue-cli-plugin-uni/packages/uni-cloud/dist/index.js';\nimport Vue from 'vue'\nimport Page from './pages/auth/login.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./login.vue?vue&type=template&id=cbd6070a&scoped=true&\"\nvar renderjs\nimport script from \"./login.vue?vue&type=script&lang=js&\"\nexport * from \"./login.vue?vue&type=script&lang=js&\"\nimport style0 from \"./login.vue?vue&type=style&index=0&id=cbd6070a&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"cbd6070a\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/auth/login.vue\"\nexport default component.exports", "export * from \"-!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./login.vue?vue&type=template&id=cbd6070a&scoped=true&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./login.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./login.vue?vue&type=script&lang=js&\"", "<template>\n  <view class=\"login-page\">\n    <!-- 顶部装饰 -->\n    <view class=\"header-section\">\n      <image class=\"logo\" src=\"/static/logo.png\"></image>\n      <text class=\"app-name\">找房租房</text>\n      <text class=\"slogan\">让租房变得更简单</text>\n    </view>\n\n    <!-- 登录表单 -->\n    <view class=\"login-section\">\n      <view class=\"login-title\">欢迎使用</view>\n      <view class=\"login-desc\">请使用微信授权登录</view>\n      \n      <!-- 微信登录按钮 -->\n      <button \n        class=\"wechat-login-btn\" \n        open-type=\"getUserInfo\"\n        @getuserinfo=\"onGetUserInfo\"\n        :disabled=\"logging\"\n      >\n        <uni-icons type=\"weixin\" size=\"24\" color=\"#fff\"></uni-icons>\n        <text class=\"btn-text\">{{ logging ? '登录中...' : '微信授权登录' }}</text>\n      </button>\n      \n      <!-- 手机号登录 -->\n      <view class=\"phone-login-section\">\n        <view class=\"divider\">\n          <text class=\"divider-text\">或</text>\n        </view>\n        \n        <view class=\"phone-input\">\n          <input \n            class=\"phone-field\" \n            v-model=\"phoneNumber\" \n            placeholder=\"请输入手机号\"\n            type=\"number\"\n            maxlength=\"11\"\n          />\n        </view>\n        \n        <view class=\"code-input\">\n          <input \n            class=\"code-field\" \n            v-model=\"verifyCode\" \n            placeholder=\"请输入验证码\"\n            type=\"number\"\n            maxlength=\"6\"\n          />\n          <button \n            class=\"send-code-btn\" \n            @click=\"sendCode\"\n            :disabled=\"codeSending || countdown > 0\"\n          >\n            {{ countdown > 0 ? `${countdown}s` : '发送验证码' }}\n          </button>\n        </view>\n        \n        <button \n          class=\"phone-login-btn\" \n          @click=\"phoneLogin\"\n          :disabled=\"!canPhoneLogin || logging\"\n        >\n          手机号登录\n        </button>\n      </view>\n    </view>\n\n    <!-- 协议条款 -->\n    <view class=\"agreement-section\">\n      <view class=\"agreement-text\">\n        <text>登录即表示同意</text>\n        <text class=\"link\" @click=\"showUserAgreement\">《用户协议》</text>\n        <text>和</text>\n        <text class=\"link\" @click=\"showPrivacyPolicy\">《隐私政策》</text>\n      </view>\n    </view>\n\n    <!-- 底部装饰 -->\n    <view class=\"footer-section\">\n      <text class=\"footer-text\">安全 · 便捷 · 可靠</text>\n    </view>\n  </view>\n</template>\n\n<script>\nimport { authAPI } from '@/utils/request.js'\n\nexport default {\n  data() {\n    return {\n      logging: false,\n      codeSending: false,\n      countdown: 0,\n      phoneNumber: '',\n      verifyCode: '',\n      timer: null\n    }\n  },\n  \n  computed: {\n    canPhoneLogin() {\n      return this.phoneNumber.length === 11 && this.verifyCode.length === 6\n    }\n  },\n  \n  onUnload() {\n    if (this.timer) {\n      clearInterval(this.timer)\n    }\n  },\n  \n  methods: {\n    // 微信授权登录\n    async onGetUserInfo(e) {\n      if (e.detail.errMsg !== 'getUserInfo:ok') {\n        uni.showToast({\n          title: '授权失败',\n          icon: 'none'\n        })\n        return\n      }\n      \n      try {\n        this.logging = true\n        \n        // 获取微信登录code\n        const loginRes = await this.getWechatCode()\n        \n        // 调用登录接口\n        const result = await authAPI.login(loginRes.code)\n        \n        // 保存登录信息\n        uni.setStorageSync('token', result.data.token)\n        uni.setStorageSync('userInfo', result.data.userInfo)\n        \n        uni.showToast({\n          title: '登录成功',\n          icon: 'success'\n        })\n        \n        // 返回上一页或跳转到首页\n        setTimeout(() => {\n          const pages = getCurrentPages()\n          if (pages.length > 1) {\n            uni.navigateBack()\n          } else {\n            uni.switchTab({\n              url: '/pages/index/index'\n            })\n          }\n        }, 1500)\n        \n      } catch (error) {\n        console.error('微信登录失败：', error)\n        uni.showToast({\n          title: error.message || '登录失败',\n          icon: 'none'\n        })\n      } finally {\n        this.logging = false\n      }\n    },\n    \n    // 获取微信登录code\n    getWechatCode() {\n      return new Promise((resolve, reject) => {\n        uni.login({\n          provider: 'weixin',\n          success: resolve,\n          fail: reject\n        })\n      })\n    },\n    \n    // 发送验证码\n    async sendCode() {\n      if (!this.phoneNumber) {\n        uni.showToast({\n          title: '请输入手机号',\n          icon: 'none'\n        })\n        return\n      }\n      \n      if (!/^1[3-9]\\d{9}$/.test(this.phoneNumber)) {\n        uni.showToast({\n          title: '手机号格式不正确',\n          icon: 'none'\n        })\n        return\n      }\n      \n      try {\n        this.codeSending = true\n        \n        // 调用发送验证码接口\n        // await authAPI.sendSmsCode(this.phoneNumber)\n        \n        // 模拟发送成功\n        uni.showToast({\n          title: '验证码已发送',\n          icon: 'success'\n        })\n        \n        // 开始倒计时\n        this.startCountdown()\n        \n      } catch (error) {\n        console.error('发送验证码失败：', error)\n        uni.showToast({\n          title: error.message || '发送失败',\n          icon: 'none'\n        })\n      } finally {\n        this.codeSending = false\n      }\n    },\n    \n    // 开始倒计时\n    startCountdown() {\n      this.countdown = 60\n      this.timer = setInterval(() => {\n        this.countdown--\n        if (this.countdown <= 0) {\n          clearInterval(this.timer)\n          this.timer = null\n        }\n      }, 1000)\n    },\n    \n    // 手机号登录\n    async phoneLogin() {\n      try {\n        this.logging = true\n        \n        // 调用手机号登录接口\n        // const result = await authAPI.phoneLogin(this.phoneNumber, this.verifyCode)\n        \n        // 模拟登录成功\n        const mockResult = {\n          data: {\n            token: 'mock_token_' + Date.now(),\n            userInfo: {\n              _id: 'mock_user_id',\n              nickname: this.phoneNumber.replace(/(\\d{3})\\d{4}(\\d{4})/, '$1****$2'),\n              avatar: '',\n              phone: this.phoneNumber\n            }\n          }\n        }\n        \n        // 保存登录信息\n        uni.setStorageSync('token', mockResult.data.token)\n        uni.setStorageSync('userInfo', mockResult.data.userInfo)\n        \n        uni.showToast({\n          title: '登录成功',\n          icon: 'success'\n        })\n        \n        // 返回上一页或跳转到首页\n        setTimeout(() => {\n          const pages = getCurrentPages()\n          if (pages.length > 1) {\n            uni.navigateBack()\n          } else {\n            uni.switchTab({\n              url: '/pages/index/index'\n            })\n          }\n        }, 1500)\n        \n      } catch (error) {\n        console.error('手机号登录失败：', error)\n        uni.showToast({\n          title: error.message || '登录失败',\n          icon: 'none'\n        })\n      } finally {\n        this.logging = false\n      }\n    },\n    \n    // 显示用户协议\n    showUserAgreement() {\n      uni.navigateTo({\n        url: '/pages/profile/agreement?type=user'\n      })\n    },\n    \n    // 显示隐私政策\n    showPrivacyPolicy() {\n      uni.navigateTo({\n        url: '/pages/profile/agreement?type=privacy'\n      })\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.login-page {\n  min-height: 100vh;\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  display: flex;\n  flex-direction: column;\n  padding: $spacing-xl;\n}\n\n/* 顶部装饰 */\n.header-section {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  margin-top: 120rpx;\n  margin-bottom: 80rpx;\n  \n  .logo {\n    width: 120rpx;\n    height: 120rpx;\n    border-radius: 24rpx;\n    margin-bottom: $spacing-lg;\n  }\n  \n  .app-name {\n    font-size: 48rpx;\n    font-weight: 600;\n    color: #fff;\n    margin-bottom: $spacing-sm;\n  }\n  \n  .slogan {\n    font-size: 28rpx;\n    color: rgba(255, 255, 255, 0.8);\n  }\n}\n\n/* 登录区域 */\n.login-section {\n  background-color: #fff;\n  border-radius: 24rpx;\n  padding: $spacing-xl;\n  margin-bottom: $spacing-xl;\n  \n  .login-title {\n    font-size: 36rpx;\n    font-weight: 600;\n    color: $text-color-primary;\n    text-align: center;\n    margin-bottom: $spacing-sm;\n  }\n  \n  .login-desc {\n    font-size: 26rpx;\n    color: $text-color-tertiary;\n    text-align: center;\n    margin-bottom: $spacing-xl;\n  }\n  \n  .wechat-login-btn {\n    width: 100%;\n    height: 88rpx;\n    background-color: #07C160;\n    color: #fff;\n    font-size: 30rpx;\n    border: none;\n    border-radius: $border-radius-base;\n    display: flex;\n    align-items: center;\n    justify-content: center;\n    margin-bottom: $spacing-lg;\n    \n    .btn-text {\n      margin-left: $spacing-sm;\n    }\n    \n    &:disabled {\n      background-color: #ccc;\n    }\n  }\n}\n\n/* 手机号登录 */\n.phone-login-section {\n  .divider {\n    display: flex;\n    align-items: center;\n    margin: $spacing-xl 0;\n    \n    &::before,\n    &::after {\n      content: '';\n      flex: 1;\n      height: 1px;\n      background-color: $border-color-light;\n    }\n    \n    .divider-text {\n      padding: 0 $spacing-md;\n      font-size: 24rpx;\n      color: $text-color-tertiary;\n    }\n  }\n  \n  .phone-input,\n  .code-input {\n    margin-bottom: $spacing-lg;\n  }\n  \n  .phone-field,\n  .code-field {\n    width: 100%;\n    height: 80rpx;\n    padding: 0 $spacing-md;\n    background-color: $bg-color-light;\n    border: 1px solid $border-color-light;\n    border-radius: $border-radius-base;\n    font-size: 28rpx;\n    \n    &::placeholder {\n      color: $text-color-quaternary;\n    }\n  }\n  \n  .code-input {\n    display: flex;\n    gap: $spacing-md;\n    \n    .code-field {\n      flex: 1;\n    }\n    \n    .send-code-btn {\n      width: 200rpx;\n      height: 80rpx;\n      background-color: $primary-color;\n      color: #fff;\n      font-size: 24rpx;\n      border: none;\n      border-radius: $border-radius-base;\n      \n      &:disabled {\n        background-color: $text-color-quaternary;\n      }\n    }\n  }\n  \n  .phone-login-btn {\n    width: 100%;\n    height: 88rpx;\n    background-color: $primary-color;\n    color: #fff;\n    font-size: 30rpx;\n    border: none;\n    border-radius: $border-radius-base;\n    \n    &:disabled {\n      background-color: $text-color-quaternary;\n    }\n  }\n}\n\n/* 协议条款 */\n.agreement-section {\n  .agreement-text {\n    text-align: center;\n    font-size: 24rpx;\n    color: rgba(255, 255, 255, 0.8);\n    \n    .link {\n      color: #fff;\n      text-decoration: underline;\n    }\n  }\n}\n\n/* 底部装饰 */\n.footer-section {\n  flex: 1;\n  display: flex;\n  align-items: flex-end;\n  justify-content: center;\n  padding-bottom: $spacing-xl;\n  \n  .footer-text {\n    font-size: 24rpx;\n    color: rgba(255, 255, 255, 0.6);\n  }\n}\n</style>\n", "import mod from \"-!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./login.vue?vue&type=style&index=0&id=cbd6070a&lang=scss&scoped=true&\"; export default mod; export * from \"-!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./login.vue?vue&type=style&index=0&id=cbd6070a&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754033370450\n      var cssReload = require(\"D:/web/wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}
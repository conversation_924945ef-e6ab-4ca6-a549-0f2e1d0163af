{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/web/project/前端10/pages/house/list.vue?17a5", "webpack:///D:/web/project/前端10/pages/house/list.vue?8a0a", "webpack:///D:/web/project/前端10/pages/house/list.vue?3dd3", "webpack:///D:/web/project/前端10/pages/house/list.vue?6a26", "uni-app:///pages/house/list.vue", "webpack:///D:/web/project/前端10/pages/house/list.vue?f99f", "webpack:///D:/web/project/前端10/pages/house/list.vue?1c84"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "loading", "refreshing", "loadingStatus", "currentPage", "pageSize", "hasMore", "houseList", "currentFilter", "<PERSON><PERSON><PERSON>", "selectedPrice", "selectedType", "selected<PERSON>ort", "areaOptions", "label", "value", "priceOptions", "typeOptions", "sortOptions", "onLoad", "methods", "loadHouseList", "refresh", "params", "page", "limit", "status", "houseAPI", "result", "newList", "console", "loadMore", "onRefresh", "goToDetail", "uni", "url", "toggleFavorite", "house", "title", "icon", "show<PERSON><PERSON><PERSON><PERSON>er", "showPriceFilter", "showTypeFilter", "showSortFilter", "closeFilter", "selectArea", "selectPrice", "selectType", "selectSort", "resetFilter", "confirmFilter", "getTypeName", "getAreaValue", "getPriceValue", "getTypeValue", "getSortValue"], "mappings": ";;;;;;;;;;;;;AAAA;AAE2D;AAC3D;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,aAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAA6H;AAC7H;AACwD;AACL;AACsC;;;AAGzF;AACyK;AACzK,gBAAgB,gLAAU;AAC1B,EAAE,0EAAM;AACR,EAAE,2FAAM;AACR,EAAE,oGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,+FAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACnBA;AAAA;AAAA;AAAA;AAA+nB,CAAgB,6nBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;ACuKnpB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eAEA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MAEAC;MAEA;MACAC;MACAC;MACAC;MACAC;MACAC;MAEA;MACAC,cACA;QAAAC;QAAAC;MAAA,GACA;QAAAD;QAAAC;MAAA,GACA;QAAAD;QAAAC;MAAA,GACA;QAAAD;QAAAC;MAAA,GACA;QAAAD;QAAAC;MAAA,GACA;QAAAD;QAAAC;MAAA,EACA;MACAC,eACA;QAAAF;QAAAC;MAAA,GACA;QAAAD;QAAAC;MAAA,GACA;QAAAD;QAAAC;MAAA,GACA;QAAAD;QAAAC;MAAA,GACA;QAAAD;QAAAC;MAAA,GACA;QAAAD;QAAAC;MAAA,EACA;MACAE,cACA;QAAAH;QAAAC;MAAA,GACA;QAAAD;QAAAC;MAAA,GACA;QAAAD;QAAAC;MAAA,GACA;QAAAD;QAAAC;MAAA,EACA;MACAG,cACA;QAAAJ;QAAAC;MAAA,GACA;QAAAD;QAAAC;MAAA,GACA;QAAAD;QAAAC;MAAA,GACA;QAAAD;QAAAC;MAAA;IAEA;EACA;EAEAI;IACA;IACA;MACA;IACA;IACA;EACA;EAEAC;IACA;IACAC;MAAA;QAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAAC;gBAAA,KACA;kBAAA;kBAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAGA;gBACA;kBACA;kBACA;kBACA;gBACA;gBAEAC;kBACAC;kBACAC;kBACAC;gBACA,GAEA;gBACA;kBACAH;gBACA;gBACA;kBACAA;gBACA;gBACA;kBACAA;gBACA;gBACA;kBACAA;gBACA;gBAAA;gBAAA,OAEAI;cAAA;gBAAAC;gBACAC;gBAEA;kBACA;gBACA;kBACA;gBACA;gBAEA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAGAC;gBACA;cAAA;gBAAA;gBAEA;gBACA;gBAAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAC;MACA;QACA;QACA;QACA;MACA;IACA;IAEA;IACAC;MACA;IACA;IAEA;IACAC;MACAC;QACAC;MACA;IACA;IAEA;IACAC;MAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA,KAEAC;kBAAA;kBAAA;gBAAA;gBAAA;gBAAA,OACAV;cAAA;gBACAU;gBACAH;kBACAI;kBACAC;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA,OAEAZ;cAAA;gBACAU;gBACAH;kBACAI;kBACAC;gBACA;cAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAGAT;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAU;MACA;MACA;IACA;IAEAC;MACA;MACA;IACA;IAEAC;MACA;MACA;IACA;IAEAC;MACA;MACA;IACA;IAEA;IACAC;MACA;IACA;IAEA;IACAC;MACA;MACA;MACA;IACA;IAEAC;MACA;MACA;MACA;IACA;IAEAC;MACA;MACA;MACA;IACA;IAEAC;MACA;MACA;MACA;IACA;IAEA;IACAC;MACA;MACA;MACA;MACA;MACA;MACA;IACA;IAEA;IACAC;MACA;MACA;IACA;IAEA;IACAC;MACA;QACA;QACA;QACA;MACA;MACA;IACA;IAEAC;MACA;QAAA;MAAA;MACA;IACA;IAEAC;MACA;QAAA;MAAA;MACA;IACA;IAEAC;MACA;QAAA;MAAA;MACA;IACA;IAEAC;MACA;QAAA;MAAA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACvaA;AAAA;AAAA;AAAA;AAA0tC,CAAgB,gpCAAG,EAAC,C;;;;;;;;;;;ACA9uC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/house/list.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;import '@dcloudio/vue-cli-plugin-uni/packages/uni-cloud/dist/index.js';\nimport Vue from 'vue'\nimport Page from './pages/house/list.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./list.vue?vue&type=template&id=145608e6&scoped=true&\"\nvar renderjs\nimport script from \"./list.vue?vue&type=script&lang=js&\"\nexport * from \"./list.vue?vue&type=script&lang=js&\"\nimport style0 from \"./list.vue?vue&type=style&index=0&id=145608e6&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"145608e6\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/house/list.vue\"\nexport default component.exports", "export * from \"-!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./list.vue?vue&type=template&id=145608e6&scoped=true&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 = _vm.houseList.length\n  var g1 = !_vm.loading && _vm.houseList.length === 0\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n        g1: g1,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./list.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./list.vue?vue&type=script&lang=js&\"", "<template>\n  <view class=\"house-list-page\">\n    <!-- 筛选栏 -->\n    <view class=\"filter-bar\">\n      <view class=\"filter-item\" @click=\"showAreaFilter\">\n        <text class=\"filter-text\">{{ selectedArea || '区域' }}</text>\n        <uni-icons type=\"down\" size=\"12\" color=\"#999\"></uni-icons>\n      </view>\n      <view class=\"filter-item\" @click=\"showPriceFilter\">\n        <text class=\"filter-text\">{{ selectedPrice || '价格' }}</text>\n        <uni-icons type=\"down\" size=\"12\" color=\"#999\"></uni-icons>\n      </view>\n      <view class=\"filter-item\" @click=\"showTypeFilter\">\n        <text class=\"filter-text\">{{ selectedType || '类型' }}</text>\n        <uni-icons type=\"down\" size=\"12\" color=\"#999\"></uni-icons>\n      </view>\n      <view class=\"filter-item\" @click=\"showSortFilter\">\n        <text class=\"filter-text\">{{ selectedSort || '排序' }}</text>\n        <uni-icons type=\"down\" size=\"12\" color=\"#999\"></uni-icons>\n      </view>\n    </view>\n\n    <!-- 房源列表 -->\n    <scroll-view \n      class=\"house-scroll\" \n      scroll-y \n      @scrolltolower=\"loadMore\"\n      refresher-enabled\n      @refresherrefresh=\"onRefresh\"\n      :refresher-triggered=\"refreshing\"\n    >\n      <view class=\"house-list\">\n        <view \n          class=\"house-item\" \n          v-for=\"house in houseList\" \n          :key=\"house._id\"\n          @click=\"goToDetail(house._id)\"\n        >\n          <image class=\"house-image\" :src=\"house.images[0]\" mode=\"aspectFill\"></image>\n          <view class=\"house-info\">\n            <view class=\"house-title\">{{ house.title }}</view>\n            <view class=\"house-desc\">{{ house.description }}</view>\n            <view class=\"house-tags\">\n              <text class=\"tag\" v-for=\"tag in house.tags\" :key=\"tag\">{{ tag }}</text>\n            </view>\n            <view class=\"house-bottom\">\n              <view class=\"price\">\n                <text class=\"price-num\">{{ house.price }}</text>\n                <text class=\"price-unit\">元/月</text>\n              </view>\n              <view class=\"location\">\n                <uni-icons type=\"location\" size=\"12\" color=\"#999\"></uni-icons>\n                <text class=\"location-text\">{{ house.address }}</text>\n              </view>\n            </view>\n          </view>\n          <view class=\"favorite-btn\" @click.stop=\"toggleFavorite(house)\">\n            <uni-icons \n              :type=\"house.is_favorite ? 'heart-filled' : 'heart'\" \n              size=\"20\" \n              :color=\"house.is_favorite ? '#FF4D4F' : '#999'\"\n            ></uni-icons>\n          </view>\n        </view>\n      </view>\n\n      <!-- 加载状态 -->\n      <view class=\"loading-more\" v-if=\"houseList.length > 0\">\n        <uni-load-more :status=\"loadingStatus\"></uni-load-more>\n      </view>\n\n      <!-- 空状态 -->\n      <view class=\"empty-state\" v-if=\"!loading && houseList.length === 0\">\n        <image class=\"empty-image\" src=\"/static/empty/no-house.png\"></image>\n        <text class=\"empty-text\">暂无房源信息</text>\n      </view>\n    </scroll-view>\n\n    <!-- 筛选弹窗 -->\n    <uni-popup ref=\"filterPopup\" type=\"bottom\">\n      <view class=\"filter-popup\">\n        <view class=\"popup-header\">\n          <text class=\"popup-title\">筛选条件</text>\n          <view class=\"popup-close\" @click=\"closeFilter\">\n            <uni-icons type=\"close\" size=\"18\" color=\"#999\"></uni-icons>\n          </view>\n        </view>\n        <view class=\"popup-content\">\n          <!-- 动态筛选内容 -->\n          <view v-if=\"currentFilter === 'area'\">\n            <view class=\"filter-group\">\n              <text class=\"group-title\">选择区域</text>\n              <view class=\"option-list\">\n                <view \n                  class=\"option-item\" \n                  v-for=\"area in areaOptions\" \n                  :key=\"area.value\"\n                  :class=\"{ active: selectedArea === area.label }\"\n                  @click=\"selectArea(area)\"\n                >\n                  {{ area.label }}\n                </view>\n              </view>\n            </view>\n          </view>\n          \n          <view v-if=\"currentFilter === 'price'\">\n            <view class=\"filter-group\">\n              <text class=\"group-title\">价格范围</text>\n              <view class=\"option-list\">\n                <view \n                  class=\"option-item\" \n                  v-for=\"price in priceOptions\" \n                  :key=\"price.value\"\n                  :class=\"{ active: selectedPrice === price.label }\"\n                  @click=\"selectPrice(price)\"\n                >\n                  {{ price.label }}\n                </view>\n              </view>\n            </view>\n          </view>\n          \n          <view v-if=\"currentFilter === 'type'\">\n            <view class=\"filter-group\">\n              <text class=\"group-title\">房源类型</text>\n              <view class=\"option-list\">\n                <view \n                  class=\"option-item\" \n                  v-for=\"type in typeOptions\" \n                  :key=\"type.value\"\n                  :class=\"{ active: selectedType === type.label }\"\n                  @click=\"selectType(type)\"\n                >\n                  {{ type.label }}\n                </view>\n              </view>\n            </view>\n          </view>\n          \n          <view v-if=\"currentFilter === 'sort'\">\n            <view class=\"filter-group\">\n              <text class=\"group-title\">排序方式</text>\n              <view class=\"option-list\">\n                <view \n                  class=\"option-item\" \n                  v-for=\"sort in sortOptions\" \n                  :key=\"sort.value\"\n                  :class=\"{ active: selectedSort === sort.label }\"\n                  @click=\"selectSort(sort)\"\n                >\n                  {{ sort.label }}\n                </view>\n              </view>\n            </view>\n          </view>\n        </view>\n        <view class=\"popup-footer\">\n          <button class=\"reset-btn\" @click=\"resetFilter\">重置</button>\n          <button class=\"confirm-btn\" @click=\"confirmFilter\">确定</button>\n        </view>\n      </view>\n    </uni-popup>\n  </view>\n</template>\n\n<script>\nimport { houseAPI } from '@/utils/request.js'\n\nexport default {\n  data() {\n    return {\n      loading: false,\n      refreshing: false,\n      loadingStatus: 'more',\n      currentPage: 1,\n      pageSize: 10,\n      hasMore: true,\n      \n      houseList: [],\n      \n      // 筛选相关\n      currentFilter: '',\n      selectedArea: '',\n      selectedPrice: '',\n      selectedType: '',\n      selectedSort: '',\n      \n      // 筛选选项\n      areaOptions: [\n        { label: '不限', value: '' },\n        { label: '朝阳区', value: 'chaoyang' },\n        { label: '海淀区', value: 'haidian' },\n        { label: '西城区', value: 'xicheng' },\n        { label: '东城区', value: 'dongcheng' },\n        { label: '丰台区', value: 'fengtai' }\n      ],\n      priceOptions: [\n        { label: '不限', value: '' },\n        { label: '1000以下', value: '0-1000' },\n        { label: '1000-2000', value: '1000-2000' },\n        { label: '2000-3000', value: '2000-3000' },\n        { label: '3000-5000', value: '3000-5000' },\n        { label: '5000以上', value: '5000-' }\n      ],\n      typeOptions: [\n        { label: '不限', value: '' },\n        { label: '整租', value: 'whole' },\n        { label: '合租', value: 'shared' },\n        { label: '短租', value: 'short' }\n      ],\n      sortOptions: [\n        { label: '默认排序', value: '' },\n        { label: '价格从低到高', value: 'price_asc' },\n        { label: '价格从高到低', value: 'price_desc' },\n        { label: '最新发布', value: 'time_desc' }\n      ]\n    }\n  },\n  \n  onLoad(options) {\n    // 处理从首页传来的类型参数\n    if (options.type) {\n      this.selectedType = this.getTypeName(options.type)\n    }\n    this.loadHouseList()\n  },\n  \n  methods: {\n    // 加载房源列表\n    async loadHouseList(refresh = false) {\n      if (this.loading) return\n      \n      try {\n        this.loading = true\n        if (refresh) {\n          this.currentPage = 1\n          this.hasMore = true\n          this.refreshing = true\n        }\n        \n        const params = {\n          page: this.currentPage,\n          limit: this.pageSize,\n          status: 'approved'\n        }\n        \n        // 添加筛选条件\n        if (this.selectedArea && this.selectedArea !== '不限') {\n          params.area = this.getAreaValue(this.selectedArea)\n        }\n        if (this.selectedPrice && this.selectedPrice !== '不限') {\n          params.price_range = this.getPriceValue(this.selectedPrice)\n        }\n        if (this.selectedType && this.selectedType !== '不限') {\n          params.type = this.getTypeValue(this.selectedType)\n        }\n        if (this.selectedSort && this.selectedSort !== '默认排序') {\n          params.sort = this.getSortValue(this.selectedSort)\n        }\n        \n        const result = await houseAPI.getHouseList(params)\n        const newList = result.data.list || []\n        \n        if (refresh) {\n          this.houseList = newList\n        } else {\n          this.houseList.push(...newList)\n        }\n        \n        this.hasMore = newList.length === this.pageSize\n        this.loadingStatus = this.hasMore ? 'more' : 'noMore'\n        \n      } catch (error) {\n        console.error('加载房源列表失败：', error)\n        this.loadingStatus = 'error'\n      } finally {\n        this.loading = false\n        this.refreshing = false\n      }\n    },\n    \n    // 加载更多\n    loadMore() {\n      if (this.hasMore && !this.loading) {\n        this.currentPage++\n        this.loadingStatus = 'loading'\n        this.loadHouseList()\n      }\n    },\n    \n    // 下拉刷新\n    onRefresh() {\n      this.loadHouseList(true)\n    },\n    \n    // 跳转到详情页\n    goToDetail(houseId) {\n      uni.navigateTo({\n        url: `/pages/house/detail?id=${houseId}`\n      })\n    },\n    \n    // 切换收藏状态\n    async toggleFavorite(house) {\n      try {\n        if (house.is_favorite) {\n          await houseAPI.unfavoriteHouse(house._id)\n          house.is_favorite = false\n          uni.showToast({\n            title: '已取消收藏',\n            icon: 'none'\n          })\n        } else {\n          await houseAPI.favoriteHouse(house._id)\n          house.is_favorite = true\n          uni.showToast({\n            title: '收藏成功',\n            icon: 'none'\n          })\n        }\n      } catch (error) {\n        console.error('收藏操作失败：', error)\n      }\n    },\n    \n    // 显示筛选弹窗\n    showAreaFilter() {\n      this.currentFilter = 'area'\n      this.$refs.filterPopup.open()\n    },\n    \n    showPriceFilter() {\n      this.currentFilter = 'price'\n      this.$refs.filterPopup.open()\n    },\n    \n    showTypeFilter() {\n      this.currentFilter = 'type'\n      this.$refs.filterPopup.open()\n    },\n    \n    showSortFilter() {\n      this.currentFilter = 'sort'\n      this.$refs.filterPopup.open()\n    },\n    \n    // 关闭筛选弹窗\n    closeFilter() {\n      this.$refs.filterPopup.close()\n    },\n    \n    // 选择筛选项\n    selectArea(area) {\n      this.selectedArea = area.label\n      this.closeFilter()\n      this.loadHouseList(true)\n    },\n    \n    selectPrice(price) {\n      this.selectedPrice = price.label\n      this.closeFilter()\n      this.loadHouseList(true)\n    },\n    \n    selectType(type) {\n      this.selectedType = type.label\n      this.closeFilter()\n      this.loadHouseList(true)\n    },\n    \n    selectSort(sort) {\n      this.selectedSort = sort.label\n      this.closeFilter()\n      this.loadHouseList(true)\n    },\n    \n    // 重置筛选\n    resetFilter() {\n      this.selectedArea = ''\n      this.selectedPrice = ''\n      this.selectedType = ''\n      this.selectedSort = ''\n      this.closeFilter()\n      this.loadHouseList(true)\n    },\n    \n    // 确认筛选\n    confirmFilter() {\n      this.closeFilter()\n      this.loadHouseList(true)\n    },\n    \n    // 辅助方法\n    getTypeName(type) {\n      const typeMap = {\n        'whole': '整租',\n        'shared': '合租',\n        'short': '短租'\n      }\n      return typeMap[type] || ''\n    },\n    \n    getAreaValue(label) {\n      const area = this.areaOptions.find(item => item.label === label)\n      return area ? area.value : ''\n    },\n    \n    getPriceValue(label) {\n      const price = this.priceOptions.find(item => item.label === label)\n      return price ? price.value : ''\n    },\n    \n    getTypeValue(label) {\n      const type = this.typeOptions.find(item => item.label === label)\n      return type ? type.value : ''\n    },\n    \n    getSortValue(label) {\n      const sort = this.sortOptions.find(item => item.label === label)\n      return sort ? sort.value : ''\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.house-list-page {\n  height: 100vh;\n  display: flex;\n  flex-direction: column;\n  background-color: $bg-color-page;\n}\n\n/* 筛选栏 */\n.filter-bar {\n  display: flex;\n  background-color: #fff;\n  border-bottom: 1px solid $border-color-light;\n\n  .filter-item {\n    flex: 1;\n    display: flex;\n    align-items: center;\n    justify-content: center;\n    height: 88rpx;\n\n    .filter-text {\n      font-size: 28rpx;\n      color: $text-color-secondary;\n      margin-right: 8rpx;\n    }\n\n    &:not(:last-child) {\n      border-right: 1px solid $border-color-light;\n    }\n  }\n}\n\n/* 房源列表 */\n.house-scroll {\n  flex: 1;\n}\n\n.house-list {\n  padding: $spacing-md;\n\n  .house-item {\n    position: relative;\n    display: flex;\n    background-color: #fff;\n    border-radius: $border-radius-large;\n    margin-bottom: $spacing-md;\n    overflow: hidden;\n    box-shadow: $box-shadow-light;\n\n    .house-image {\n      width: 240rpx;\n      height: 180rpx;\n      flex-shrink: 0;\n    }\n\n    .house-info {\n      flex: 1;\n      padding: $spacing-md;\n      display: flex;\n      flex-direction: column;\n      justify-content: space-between;\n\n      .house-title {\n        font-size: 30rpx;\n        font-weight: 600;\n        color: $text-color-primary;\n        margin-bottom: $spacing-xs;\n        overflow: hidden;\n        text-overflow: ellipsis;\n        white-space: nowrap;\n        padding-right: 60rpx;\n      }\n\n      .house-desc {\n        font-size: 24rpx;\n        color: $text-color-tertiary;\n        margin-bottom: $spacing-xs;\n        overflow: hidden;\n        text-overflow: ellipsis;\n        white-space: nowrap;\n      }\n\n      .house-tags {\n        margin-bottom: $spacing-sm;\n\n        .tag {\n          display: inline-block;\n          padding: 4rpx 12rpx;\n          background-color: $bg-color-light;\n          color: $text-color-secondary;\n          font-size: 20rpx;\n          border-radius: 8rpx;\n          margin-right: $spacing-xs;\n        }\n      }\n\n      .house-bottom {\n        display: flex;\n        justify-content: space-between;\n        align-items: flex-end;\n\n        .price {\n          .price-num {\n            font-size: 32rpx;\n            font-weight: 600;\n            color: $error-color;\n          }\n\n          .price-unit {\n            font-size: 24rpx;\n            color: $text-color-tertiary;\n          }\n        }\n\n        .location {\n          display: flex;\n          align-items: center;\n\n          .location-text {\n            margin-left: 4rpx;\n            font-size: 22rpx;\n            color: $text-color-tertiary;\n            overflow: hidden;\n            text-overflow: ellipsis;\n            white-space: nowrap;\n            max-width: 160rpx;\n          }\n        }\n      }\n    }\n\n    .favorite-btn {\n      position: absolute;\n      top: $spacing-md;\n      right: $spacing-md;\n      width: 60rpx;\n      height: 60rpx;\n      display: flex;\n      align-items: center;\n      justify-content: center;\n      background-color: rgba(255, 255, 255, 0.9);\n      border-radius: 50%;\n    }\n  }\n}\n\n/* 加载状态 */\n.loading-more {\n  padding: $spacing-lg;\n}\n\n/* 空状态 */\n.empty-state {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  padding: 120rpx $spacing-lg;\n\n  .empty-image {\n    width: 200rpx;\n    height: 200rpx;\n    margin-bottom: $spacing-lg;\n  }\n\n  .empty-text {\n    font-size: 28rpx;\n    color: $text-color-tertiary;\n  }\n}\n\n/* 筛选弹窗 */\n.filter-popup {\n  background-color: #fff;\n  border-radius: 24rpx 24rpx 0 0;\n  max-height: 80vh;\n\n  .popup-header {\n    display: flex;\n    justify-content: space-between;\n    align-items: center;\n    padding: $spacing-lg;\n    border-bottom: 1px solid $border-color-light;\n\n    .popup-title {\n      font-size: 32rpx;\n      font-weight: 600;\n      color: $text-color-primary;\n    }\n\n    .popup-close {\n      width: 60rpx;\n      height: 60rpx;\n      display: flex;\n      align-items: center;\n      justify-content: center;\n    }\n  }\n\n  .popup-content {\n    padding: $spacing-lg;\n    max-height: 60vh;\n    overflow-y: auto;\n\n    .filter-group {\n      .group-title {\n        font-size: 28rpx;\n        font-weight: 600;\n        color: $text-color-primary;\n        margin-bottom: $spacing-md;\n      }\n\n      .option-list {\n        display: flex;\n        flex-wrap: wrap;\n        gap: $spacing-md;\n\n        .option-item {\n          padding: $spacing-sm $spacing-md;\n          background-color: $bg-color-light;\n          color: $text-color-secondary;\n          font-size: 26rpx;\n          border-radius: $border-radius-base;\n          border: 1px solid transparent;\n\n          &.active {\n            background-color: rgba($primary-color, 0.1);\n            color: $primary-color;\n            border-color: $primary-color;\n          }\n        }\n      }\n    }\n  }\n\n  .popup-footer {\n    display: flex;\n    padding: $spacing-lg;\n    border-top: 1px solid $border-color-light;\n    gap: $spacing-md;\n\n    .reset-btn {\n      flex: 1;\n      height: 80rpx;\n      background-color: $bg-color-light;\n      color: $text-color-secondary;\n      border: none;\n      border-radius: $border-radius-base;\n      font-size: 28rpx;\n    }\n\n    .confirm-btn {\n      flex: 2;\n      height: 80rpx;\n      background-color: $primary-color;\n      color: #fff;\n      border: none;\n      border-radius: $border-radius-base;\n      font-size: 28rpx;\n    }\n  }\n}\n</style>\n", "import mod from \"-!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./list.vue?vue&type=style&index=0&id=145608e6&lang=scss&scoped=true&\"; export default mod; export * from \"-!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./list.vue?vue&type=style&index=0&id=145608e6&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754033370528\n      var cssReload = require(\"D:/web/wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}
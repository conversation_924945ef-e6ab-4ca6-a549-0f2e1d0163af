{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/web/project/前端10/pages/index/index.vue?4d1f", "webpack:///D:/web/project/前端10/pages/index/index.vue?fc59", "webpack:///D:/web/project/前端10/pages/index/index.vue?dc8b", "webpack:///D:/web/project/前端10/pages/index/index.vue?9a08", "uni-app:///pages/index/index.vue", "webpack:///D:/web/project/前端10/pages/index/index.vue?8eb0", "webpack:///D:/web/project/前端10/pages/index/index.vue?4320"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "currentCity", "loading", "loadingStatus", "bannerList", "image", "categoryList", "name", "icon", "color", "type", "recommendHouses", "onLoad", "onPullDownRefresh", "methods", "loadRecommendHouses", "houseAPI", "page", "limit", "status", "result", "console", "uni", "goToSearch", "url", "chooseLocation", "success", "goToCategory", "goToHouseList", "goToHouseDetail"], "mappings": ";;;;;;;;;;;;;AAAA;AAE2D;AAC3D;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,eAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAA8H;AAC9H;AACyD;AACL;AACsC;;;AAG1F;AACyK;AACzK,gBAAgB,gLAAU;AAC1B,EAAE,2EAAM;AACR,EAAE,4FAAM;AACR,EAAE,qGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,gGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACRA;AAAA;AAAA;AAAA;AAAgoB,CAAgB,8nBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;ACuFppB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eAEA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC,aACA;QACAC;MACA,GACA;QACAA;MACA,GACA;QACAA;MACA,EACA;MACAC,eACA;QACAC;QACAC;QACAC;QACAC;MACA,GACA;QACAH;QACAC;QACAC;QACAC;MACA,GACA;QACAH;QACAC;QACAC;QACAC;MACA,GACA;QACAH;QACAC;QACAC;QACAC;MACA,EACA;MACAC;IACA;EACA;EAEAC;IACA;EACA;EAEAC;IACA;EACA;EAEAC;IACA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAEA;gBACA;gBAAA;gBAAA,OAEAC;kBACAC;kBACAC;kBACAC;gBACA;cAAA;gBAJAC;gBAMA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAC;gBACA;cAAA;gBAAA;gBAEA;gBACAC;gBAAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAC;MACAD;QACAE;MACA;IACA;IAEA;IACAC;MAAA;MACAH;QACAI;UACA;QACA;MACA;IACA;IAEA;IACAC;MACA;QACA;QACAL;UACAE;QACA;MACA;QACA;QACAF;UACAE;QACA;MACA;IACA;IAEA;IACAI;MACAN;QACAE;MACA;IACA;IAEA;IACAK;MACAP;QACAE;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACrNA;AAAA;AAAA;AAAA;AAA2tC,CAAgB,ipCAAG,EAAC,C;;;;;;;;;;;ACA/uC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/index/index.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;import '@dcloudio/vue-cli-plugin-uni/packages/uni-cloud/dist/index.js';\nimport Vue from 'vue'\nimport Page from './pages/index/index.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./index.vue?vue&type=template&id=57280228&scoped=true&\"\nvar renderjs\nimport script from \"./index.vue?vue&type=script&lang=js&\"\nexport * from \"./index.vue?vue&type=script&lang=js&\"\nimport style0 from \"./index.vue?vue&type=style&index=0&id=57280228&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"57280228\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/index/index.vue\"\nexport default component.exports", "export * from \"-!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=template&id=57280228&scoped=true&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"", "<template>\r\n  <view class=\"home-page\">\r\n    <!-- 搜索栏 -->\r\n    <view class=\"search-bar\">\r\n      <view class=\"search-input\" @click=\"goToSearch\">\r\n        <uni-icons type=\"search\" size=\"18\" color=\"#999\"></uni-icons>\r\n        <text class=\"search-placeholder\">搜索房源、地址、小区</text>\r\n      </view>\r\n      <view class=\"location-btn\" @click=\"chooseLocation\">\r\n        <uni-icons type=\"location\" size=\"16\" color=\"#007AFF\"></uni-icons>\r\n        <text class=\"location-text\">{{ currentCity }}</text>\r\n      </view>\r\n    </view>\r\n\r\n    <!-- 轮播图 -->\r\n    <view class=\"banner-section\">\r\n      <swiper class=\"banner-swiper\" indicator-dots circular autoplay>\r\n        <swiper-item v-for=\"(banner, index) in bannerList\" :key=\"index\">\r\n          <image class=\"banner-image\" :src=\"banner.image\" mode=\"aspectFill\"></image>\r\n        </swiper-item>\r\n      </swiper>\r\n    </view>\r\n\r\n    <!-- 分类导航 -->\r\n    <view class=\"category-section\">\r\n      <view class=\"category-grid\">\r\n        <view\r\n          class=\"category-item\"\r\n          v-for=\"(category, index) in categoryList\"\r\n          :key=\"index\"\r\n          @click=\"goToCategory(category)\"\r\n        >\r\n          <view class=\"category-icon\">\r\n            <uni-icons :type=\"category.icon\" size=\"24\" :color=\"category.color\"></uni-icons>\r\n          </view>\r\n          <text class=\"category-text\">{{ category.name }}</text>\r\n        </view>\r\n      </view>\r\n    </view>\r\n\r\n    <!-- 推荐房源 -->\r\n    <view class=\"recommend-section\">\r\n      <view class=\"section-header\">\r\n        <text class=\"section-title\">推荐房源</text>\r\n        <view class=\"more-btn\" @click=\"goToHouseList\">\r\n          <text class=\"more-text\">更多</text>\r\n          <uni-icons type=\"right\" size=\"14\" color=\"#999\"></uni-icons>\r\n        </view>\r\n      </view>\r\n\r\n      <view class=\"house-list\">\r\n        <view\r\n          class=\"house-item\"\r\n          v-for=\"house in recommendHouses\"\r\n          :key=\"house._id\"\r\n          @click=\"goToHouseDetail(house._id)\"\r\n        >\r\n          <image class=\"house-image\" :src=\"house.images[0]\" mode=\"aspectFill\"></image>\r\n          <view class=\"house-info\">\r\n            <view class=\"house-title\">{{ house.title }}</view>\r\n            <view class=\"house-desc\">{{ house.description }}</view>\r\n            <view class=\"house-tags\">\r\n              <text class=\"tag\" v-for=\"tag in house.tags\" :key=\"tag\">{{ tag }}</text>\r\n            </view>\r\n            <view class=\"house-bottom\">\r\n              <view class=\"price\">\r\n                <text class=\"price-num\">{{ house.price }}</text>\r\n                <text class=\"price-unit\">元/月</text>\r\n              </view>\r\n              <view class=\"location\">\r\n                <uni-icons type=\"location\" size=\"12\" color=\"#999\"></uni-icons>\r\n                <text class=\"location-text\">{{ house.address }}</text>\r\n              </view>\r\n            </view>\r\n          </view>\r\n        </view>\r\n      </view>\r\n    </view>\r\n\r\n    <!-- 加载更多 -->\r\n    <view class=\"loading-more\" v-if=\"loading\">\r\n      <uni-load-more :status=\"loadingStatus\"></uni-load-more>\r\n    </view>\r\n  </view>\r\n</template>\r\n\r\n<script>\r\nimport { houseAPI } from '@/utils/request.js'\r\n\r\nexport default {\r\n  data() {\r\n    return {\r\n      currentCity: '北京',\r\n      loading: false,\r\n      loadingStatus: 'more',\r\n      bannerList: [\r\n        {\r\n          image: '/static/banner/banner1.jpg'\r\n        },\r\n        {\r\n          image: '/static/banner/banner2.jpg'\r\n        },\r\n        {\r\n          image: '/static/banner/banner3.jpg'\r\n        }\r\n      ],\r\n      categoryList: [\r\n        {\r\n          name: '整租',\r\n          icon: 'home',\r\n          color: '#007AFF',\r\n          type: 'whole'\r\n        },\r\n        {\r\n          name: '合租',\r\n          icon: 'person',\r\n          color: '#52C41A',\r\n          type: 'shared'\r\n        },\r\n        {\r\n          name: '短租',\r\n          icon: 'calendar',\r\n          color: '#FAAD14',\r\n          type: 'short'\r\n        },\r\n        {\r\n          name: '地图找房',\r\n          icon: 'map',\r\n          color: '#FF4D4F',\r\n          type: 'map'\r\n        }\r\n      ],\r\n      recommendHouses: []\r\n    }\r\n  },\r\n\r\n  onLoad() {\r\n    this.loadRecommendHouses()\r\n  },\r\n\r\n  onPullDownRefresh() {\r\n    this.loadRecommendHouses()\r\n  },\r\n\r\n  methods: {\r\n    // 加载推荐房源\r\n    async loadRecommendHouses() {\r\n      try {\r\n        this.loading = true\r\n        this.loadingStatus = 'loading'\r\n\r\n        const result = await houseAPI.getHouseList({\r\n          page: 1,\r\n          limit: 10,\r\n          status: 'approved'\r\n        })\r\n\r\n        this.recommendHouses = result.data.list || []\r\n        this.loadingStatus = 'noMore'\r\n      } catch (error) {\r\n        console.error('加载推荐房源失败：', error)\r\n        this.loadingStatus = 'error'\r\n      } finally {\r\n        this.loading = false\r\n        uni.stopPullDownRefresh()\r\n      }\r\n    },\r\n\r\n    // 跳转到搜索页面\r\n    goToSearch() {\r\n      uni.navigateTo({\r\n        url: '/pages/house/search'\r\n      })\r\n    },\r\n\r\n    // 选择位置\r\n    chooseLocation() {\r\n      uni.chooseLocation({\r\n        success: (res) => {\r\n          this.currentCity = res.name || res.address\r\n        }\r\n      })\r\n    },\r\n\r\n    // 跳转到分类页面\r\n    goToCategory(category) {\r\n      if (category.type === 'map') {\r\n        // 跳转到地图找房\r\n        uni.navigateTo({\r\n          url: '/pages/house/map'\r\n        })\r\n      } else {\r\n        // 跳转到房源列表\r\n        uni.navigateTo({\r\n          url: `/pages/house/list?type=${category.type}`\r\n        })\r\n      }\r\n    },\r\n\r\n    // 跳转到房源列表\r\n    goToHouseList() {\r\n      uni.switchTab({\r\n        url: '/pages/house/list'\r\n      })\r\n    },\r\n\r\n    // 跳转到房源详情\r\n    goToHouseDetail(houseId) {\r\n      uni.navigateTo({\r\n        url: `/pages/house/detail?id=${houseId}`\r\n      })\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.home-page {\r\n  background-color: $bg-color-page;\r\n  min-height: 100vh;\r\n}\r\n\r\n/* 搜索栏 */\r\n.search-bar {\r\n  display: flex;\r\n  align-items: center;\r\n  padding: $spacing-md;\r\n  background-color: #fff;\r\n\r\n  .search-input {\r\n    flex: 1;\r\n    display: flex;\r\n    align-items: center;\r\n    height: 72rpx;\r\n    padding: 0 $spacing-md;\r\n    background-color: $bg-color-light;\r\n    border-radius: 36rpx;\r\n    margin-right: $spacing-md;\r\n\r\n    .search-placeholder {\r\n      margin-left: $spacing-xs;\r\n      color: $text-color-tertiary;\r\n      font-size: 28rpx;\r\n    }\r\n  }\r\n\r\n  .location-btn {\r\n    display: flex;\r\n    align-items: center;\r\n\r\n    .location-text {\r\n      margin-left: 4rpx;\r\n      color: $primary-color;\r\n      font-size: 26rpx;\r\n    }\r\n  }\r\n}\r\n\r\n/* 轮播图 */\r\n.banner-section {\r\n  margin: $spacing-md;\r\n  border-radius: $border-radius-large;\r\n  overflow: hidden;\r\n\r\n  .banner-swiper {\r\n    height: 320rpx;\r\n\r\n    .banner-image {\r\n      width: 100%;\r\n      height: 100%;\r\n    }\r\n  }\r\n}\r\n\r\n/* 分类导航 */\r\n.category-section {\r\n  margin: $spacing-md;\r\n  padding: $spacing-lg;\r\n  background-color: #fff;\r\n  border-radius: $border-radius-large;\r\n\r\n  .category-grid {\r\n    display: flex;\r\n    justify-content: space-between;\r\n\r\n    .category-item {\r\n      display: flex;\r\n      flex-direction: column;\r\n      align-items: center;\r\n      flex: 1;\r\n\r\n      .category-icon {\r\n        width: 88rpx;\r\n        height: 88rpx;\r\n        display: flex;\r\n        align-items: center;\r\n        justify-content: center;\r\n        background-color: $bg-color-light;\r\n        border-radius: $border-radius-large;\r\n        margin-bottom: $spacing-sm;\r\n      }\r\n\r\n      .category-text {\r\n        font-size: 26rpx;\r\n        color: $text-color-secondary;\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n/* 推荐房源 */\r\n.recommend-section {\r\n  margin: $spacing-md;\r\n\r\n  .section-header {\r\n    display: flex;\r\n    justify-content: space-between;\r\n    align-items: center;\r\n    margin-bottom: $spacing-md;\r\n\r\n    .section-title {\r\n      font-size: 32rpx;\r\n      font-weight: 600;\r\n      color: $text-color-primary;\r\n    }\r\n\r\n    .more-btn {\r\n      display: flex;\r\n      align-items: center;\r\n\r\n      .more-text {\r\n        font-size: 26rpx;\r\n        color: $text-color-tertiary;\r\n        margin-right: 4rpx;\r\n      }\r\n    }\r\n  }\r\n\r\n  .house-list {\r\n    .house-item {\r\n      display: flex;\r\n      background-color: #fff;\r\n      border-radius: $border-radius-large;\r\n      margin-bottom: $spacing-md;\r\n      overflow: hidden;\r\n      box-shadow: $box-shadow-light;\r\n\r\n      .house-image {\r\n        width: 240rpx;\r\n        height: 180rpx;\r\n        flex-shrink: 0;\r\n      }\r\n\r\n      .house-info {\r\n        flex: 1;\r\n        padding: $spacing-md;\r\n        display: flex;\r\n        flex-direction: column;\r\n        justify-content: space-between;\r\n\r\n        .house-title {\r\n          font-size: 30rpx;\r\n          font-weight: 600;\r\n          color: $text-color-primary;\r\n          margin-bottom: $spacing-xs;\r\n          overflow: hidden;\r\n          text-overflow: ellipsis;\r\n          white-space: nowrap;\r\n        }\r\n\r\n        .house-desc {\r\n          font-size: 24rpx;\r\n          color: $text-color-tertiary;\r\n          margin-bottom: $spacing-xs;\r\n          overflow: hidden;\r\n          text-overflow: ellipsis;\r\n          white-space: nowrap;\r\n        }\r\n\r\n        .house-tags {\r\n          margin-bottom: $spacing-sm;\r\n\r\n          .tag {\r\n            display: inline-block;\r\n            padding: 4rpx 12rpx;\r\n            background-color: $bg-color-light;\r\n            color: $text-color-secondary;\r\n            font-size: 20rpx;\r\n            border-radius: 8rpx;\r\n            margin-right: $spacing-xs;\r\n          }\r\n        }\r\n\r\n        .house-bottom {\r\n          display: flex;\r\n          justify-content: space-between;\r\n          align-items: flex-end;\r\n\r\n          .price {\r\n            .price-num {\r\n              font-size: 32rpx;\r\n              font-weight: 600;\r\n              color: $error-color;\r\n            }\r\n\r\n            .price-unit {\r\n              font-size: 24rpx;\r\n              color: $text-color-tertiary;\r\n            }\r\n          }\r\n\r\n          .location {\r\n            display: flex;\r\n            align-items: center;\r\n\r\n            .location-text {\r\n              margin-left: 4rpx;\r\n              font-size: 22rpx;\r\n              color: $text-color-tertiary;\r\n              overflow: hidden;\r\n              text-overflow: ellipsis;\r\n              white-space: nowrap;\r\n              max-width: 160rpx;\r\n            }\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n/* 加载更多 */\r\n.loading-more {\r\n  padding: $spacing-lg;\r\n}\r\n</style>\r\n", "import mod from \"-!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&id=57280228&lang=scss&scoped=true&\"; export default mod; export * from \"-!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&id=57280228&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754033370493\n      var cssReload = require(\"D:/web/wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}
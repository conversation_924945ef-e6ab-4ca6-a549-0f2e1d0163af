<template>
  <view class="profile-page">
    <!-- 用户信息 -->
    <view class="user-section">
      <view class="user-info" @click="goToLogin" v-if="!userInfo.nickname">
        <image class="avatar" src="/static/default-avatar.png"></image>
        <view class="user-details">
          <text class="nickname">点击登录</text>
          <text class="desc">登录后享受更多服务</text>
        </view>
        <uni-icons type="right" size="16" color="#999"></uni-icons>
      </view>
      
      <view class="user-info" v-else>
        <image class="avatar" :src="userInfo.avatar || '/static/default-avatar.png'"></image>
        <view class="user-details">
          <text class="nickname">{{ userInfo.nickname }}</text>
          <text class="desc">{{ userInfo.phone || '未绑定手机号' }}</text>
        </view>
        <view class="edit-btn" @click="editProfile">
          <uni-icons type="compose" size="16" color="#999"></uni-icons>
        </view>
      </view>
    </view>

    <!-- 数据统计 -->
    <view class="stats-section" v-if="userInfo.nickname">
      <view class="stats-grid">
        <view class="stats-item" @click="goToMyHouses">
          <text class="stats-num">{{ stats.myHouses }}</text>
          <text class="stats-label">我的发布</text>
        </view>
        <view class="stats-item" @click="goToFavorites">
          <text class="stats-num">{{ stats.favorites }}</text>
          <text class="stats-label">我的收藏</text>
        </view>
        <view class="stats-item" @click="goToHistory">
          <text class="stats-num">{{ stats.viewHistory }}</text>
          <text class="stats-label">浏览记录</text>
        </view>
      </view>
    </view>

    <!-- 功能菜单 -->
    <view class="menu-section">
      <view class="menu-group">
        <view class="menu-item" @click="goToMyHouses">
          <view class="menu-left">
            <uni-icons type="home" size="20" color="#007AFF"></uni-icons>
            <text class="menu-text">我的发布</text>
          </view>
          <uni-icons type="right" size="14" color="#999"></uni-icons>
        </view>
        
        <view class="menu-item" @click="goToFavorites">
          <view class="menu-left">
            <uni-icons type="heart" size="20" color="#FF4D4F"></uni-icons>
            <text class="menu-text">我的收藏</text>
          </view>
          <uni-icons type="right" size="14" color="#999"></uni-icons>
        </view>
        
        <view class="menu-item" @click="goToHistory">
          <view class="menu-left">
            <uni-icons type="eye" size="20" color="#52C41A"></uni-icons>
            <text class="menu-text">浏览记录</text>
          </view>
          <uni-icons type="right" size="14" color="#999"></uni-icons>
        </view>
      </view>
      
      <view class="menu-group">
        <view class="menu-item" @click="contactService">
          <view class="menu-left">
            <uni-icons type="chatbubble" size="20" color="#FAAD14"></uni-icons>
            <text class="menu-text">联系客服</text>
          </view>
          <uni-icons type="right" size="14" color="#999"></uni-icons>
        </view>
        
        <view class="menu-item" @click="goToFeedback">
          <view class="menu-left">
            <uni-icons type="compose" size="20" color="#722ED1"></uni-icons>
            <text class="menu-text">意见反馈</text>
          </view>
          <uni-icons type="right" size="14" color="#999"></uni-icons>
        </view>
        
        <view class="menu-item" @click="goToAbout">
          <view class="menu-left">
            <uni-icons type="info" size="20" color="#13C2C2"></uni-icons>
            <text class="menu-text">关于我们</text>
          </view>
          <uni-icons type="right" size="14" color="#999"></uni-icons>
        </view>
      </view>
      
      <view class="menu-group">
        <view class="menu-item" @click="goToSettings">
          <view class="menu-left">
            <uni-icons type="gear" size="20" color="#999"></uni-icons>
            <text class="menu-text">设置</text>
          </view>
          <uni-icons type="right" size="14" color="#999"></uni-icons>
        </view>
      </view>
    </view>

    <!-- 退出登录 -->
    <view class="logout-section" v-if="userInfo.nickname">
      <button class="logout-btn" @click="logout">退出登录</button>
    </view>
  </view>
</template>

<script>
import { authAPI } from '@/utils/request.js'

export default {
  data() {
    return {
      userInfo: {},
      stats: {
        myHouses: 0,
        favorites: 0,
        viewHistory: 0
      }
    }
  },
  
  onShow() {
    this.loadUserInfo()
    this.loadStats()
  },
  
  methods: {
    // 加载用户信息
    loadUserInfo() {
      const userInfo = uni.getStorageSync('userInfo')
      if (userInfo) {
        this.userInfo = userInfo
      }
    },
    
    // 加载统计数据
    async loadStats() {
      if (!this.userInfo.nickname) return
      
      try {
        // 这里可以调用相应的API获取统计数据
        // const result = await authAPI.getUserStats()
        // this.stats = result.data
        
        // 临时模拟数据
        this.stats = {
          myHouses: 3,
          favorites: 8,
          viewHistory: 15
        }
      } catch (error) {
        console.error('加载统计数据失败：', error)
      }
    },
    
    // 跳转到登录页面
    goToLogin() {
      uni.navigateTo({
        url: '/pages/auth/login'
      })
    },
    
    // 编辑个人资料
    editProfile() {
      uni.navigateTo({
        url: '/pages/profile/edit'
      })
    },
    
    // 跳转到我的发布
    goToMyHouses() {
      if (!this.checkLogin()) return
      uni.navigateTo({
        url: '/pages/publish/my'
      })
    },
    
    // 跳转到我的收藏
    goToFavorites() {
      if (!this.checkLogin()) return
      uni.navigateTo({
        url: '/pages/profile/favorite'
      })
    },
    
    // 跳转到浏览记录
    goToHistory() {
      if (!this.checkLogin()) return
      uni.navigateTo({
        url: '/pages/profile/history'
      })
    },
    
    // 联系客服
    contactService() {
      uni.makePhoneCall({
        phoneNumber: '************'
      })
    },
    
    // 意见反馈
    goToFeedback() {
      uni.navigateTo({
        url: '/pages/profile/feedback'
      })
    },
    
    // 关于我们
    goToAbout() {
      uni.navigateTo({
        url: '/pages/profile/about'
      })
    },
    
    // 设置
    goToSettings() {
      uni.navigateTo({
        url: '/pages/profile/settings'
      })
    },
    
    // 检查登录状态
    checkLogin() {
      if (!this.userInfo.nickname) {
        uni.showModal({
          title: '提示',
          content: '请先登录',
          success: (res) => {
            if (res.confirm) {
              this.goToLogin()
            }
          }
        })
        return false
      }
      return true
    },
    
    // 退出登录
    logout() {
      uni.showModal({
        title: '提示',
        content: '确定要退出登录吗？',
        success: (res) => {
          if (res.confirm) {
            // 清除本地存储
            uni.removeStorageSync('token')
            uni.removeStorageSync('userInfo')
            
            // 重置数据
            this.userInfo = {}
            this.stats = {
              myHouses: 0,
              favorites: 0,
              viewHistory: 0
            }
            
            uni.showToast({
              title: '已退出登录',
              icon: 'success'
            })
          }
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.profile-page {
  background-color: $bg-color-page;
  min-height: 100vh;
}

/* 用户信息 */
.user-section {
  background-color: #fff;
  margin-bottom: $spacing-md;
  
  .user-info {
    display: flex;
    align-items: center;
    padding: $spacing-xl $spacing-lg;
    
    .avatar {
      width: 120rpx;
      height: 120rpx;
      border-radius: 50%;
      margin-right: $spacing-lg;
    }
    
    .user-details {
      flex: 1;
      
      .nickname {
        display: block;
        font-size: 36rpx;
        font-weight: 600;
        color: $text-color-primary;
        margin-bottom: $spacing-xs;
      }
      
      .desc {
        font-size: 26rpx;
        color: $text-color-tertiary;
      }
    }
    
    .edit-btn {
      width: 60rpx;
      height: 60rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      background-color: $bg-color-light;
      border-radius: 50%;
    }
  }
}

/* 数据统计 */
.stats-section {
  background-color: #fff;
  margin-bottom: $spacing-md;
  padding: $spacing-lg;
  
  .stats-grid {
    display: flex;
    
    .stats-item {
      flex: 1;
      display: flex;
      flex-direction: column;
      align-items: center;
      
      .stats-num {
        font-size: 48rpx;
        font-weight: 600;
        color: $primary-color;
        margin-bottom: $spacing-xs;
      }
      
      .stats-label {
        font-size: 26rpx;
        color: $text-color-secondary;
      }
    }
  }
}

/* 功能菜单 */
.menu-section {
  .menu-group {
    background-color: #fff;
    margin-bottom: $spacing-md;
    
    .menu-item {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: $spacing-lg;
      border-bottom: 1px solid $border-color-lighter;
      
      &:last-child {
        border-bottom: none;
      }
      
      .menu-left {
        display: flex;
        align-items: center;
        
        .menu-text {
          margin-left: $spacing-md;
          font-size: 30rpx;
          color: $text-color-primary;
        }
      }
    }
  }
}

/* 退出登录 */
.logout-section {
  padding: $spacing-lg;
  
  .logout-btn {
    width: 100%;
    height: 88rpx;
    background-color: #fff;
    color: $error-color;
    font-size: 30rpx;
    border: 1px solid $border-color-base;
    border-radius: $border-radius-base;
  }
}
</style>

/**
 * 网络请求工具
 */

// 请求基础配置
const config = {
  baseUrl: '', // uniCloud 不需要配置 baseUrl
  timeout: 10000,
  header: {
    'Content-Type': 'application/json'
  }
}

// 请求拦截器
const requestInterceptor = (options) => {
  // 显示加载提示
  uni.showLoading({
    title: '加载中...',
    mask: true
  })
  
  // 添加 token
  const token = uni.getStorageSync('token')
  if (token) {
    options.data = options.data || {}
    options.data.uniIdToken = token
  }
  
  return options
}

// 响应拦截器
const responseInterceptor = (response) => {
  // 隐藏加载提示
  uni.hideLoading()
  
  const { data } = response
  
  // 统一错误处理
  if (data.code !== 0) {
    // token 失效，跳转登录
    if (data.code === 2001 || data.code === 2002) {
      uni.removeStorageSync('token')
      uni.removeStorageSync('userInfo')
      uni.reLaunch({
        url: '/pages/auth/login'
      })
      return Promise.reject(data)
    }
    
    // 其他错误提示
    uni.showToast({
      title: data.message || '请求失败',
      icon: 'none',
      duration: 2000
    })
    return Promise.reject(data)
  }
  
  return data
}

// 云函数请求封装
const cloudRequest = (name, data = {}) => {
  const options = requestInterceptor({
    name,
    data
  })
  
  return new Promise((resolve, reject) => {
    uniCloud.callFunction({
      name: options.name,
      data: options.data,
      success: (res) => {
        try {
          const result = responseInterceptor(res)
          resolve(result)
        } catch (error) {
          reject(error)
        }
      },
      fail: (error) => {
        uni.hideLoading()
        uni.showToast({
          title: '网络请求失败',
          icon: 'none'
        })
        reject(error)
      }
    })
  })
}

// 用户认证相关 API
export const authAPI = {
  // 微信登录
  login: (code) => cloudRequest('user-auth', { action: 'login', code }),
  
  // 获取用户信息
  getUserInfo: () => cloudRequest('user-auth', { action: 'getUserInfo' }),
  
  // 更新用户信息
  updateUserInfo: (userInfo) => cloudRequest('user-auth', { 
    action: 'updateUserInfo', 
    ...userInfo 
  })
}

// 房源管理相关 API
export const houseAPI = {
  // 获取房源列表
  getHouseList: (params = {}) => cloudRequest('house-manage', { 
    action: 'getHouseList', 
    ...params 
  }),
  
  // 获取房源详情
  getHouseDetail: (house_id) => cloudRequest('house-manage', { 
    action: 'getHouseDetail', 
    house_id 
  }),
  
  // 发布房源
  publishHouse: (houseData) => cloudRequest('house-manage', { 
    action: 'publishHouse', 
    ...houseData 
  }),
  
  // 更新房源
  updateHouse: (house_id, houseData) => cloudRequest('house-manage', { 
    action: 'updateHouse', 
    house_id,
    ...houseData 
  }),
  
  // 删除房源
  deleteHouse: (house_id) => cloudRequest('house-manage', { 
    action: 'deleteHouse', 
    house_id 
  }),
  
  // 收藏房源
  favoriteHouse: (house_id) => cloudRequest('house-manage', { 
    action: 'favoriteHouse', 
    house_id 
  }),
  
  // 取消收藏
  unfavoriteHouse: (house_id) => cloudRequest('house-manage', { 
    action: 'unfavoriteHouse', 
    house_id 
  }),
  
  // 获取我的收藏
  getMyFavorites: () => cloudRequest('house-manage', { 
    action: 'getMyFavorites' 
  }),
  
  // 获取我的房源
  getMyHouses: () => cloudRequest('house-manage', { 
    action: 'getMyHouses' 
  })
}

// 文件上传相关 API
export const uploadAPI = {
  // 上传图片
  uploadImage: (filePath) => {
    return new Promise((resolve, reject) => {
      uni.showLoading({
        title: '上传中...'
      })
      
      uniCloud.uploadFile({
        filePath,
        cloudPath: `house-images/${Date.now()}-${Math.random().toString(36).substr(2, 9)}.jpg`,
        success: (res) => {
          uni.hideLoading()
          resolve(res.fileID)
        },
        fail: (error) => {
          uni.hideLoading()
          uni.showToast({
            title: '上传失败',
            icon: 'none'
          })
          reject(error)
        }
      })
    })
  }
}

// 默认导出
export default {
  authAPI,
  houseAPI,
  uploadAPI
}

@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 自定义项目样式变量 */
/* 主题色 */
/* 辅助色 */
/* 中性色 */
/* 背景色 */
/* 边框色 */
/* 阴影 */
/* 圆角 */
/* 间距 */
.favorite-page.data-v-069a0a8a {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: #F5F5F5;
}
/* 顶部操作栏 */
.header-bar.data-v-069a0a8a {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 24rpx 32rpx;
  background-color: #fff;
  border-bottom: 1px solid #E8E8E8;
}
.header-bar .select-all.data-v-069a0a8a {
  display: flex;
  align-items: center;
}
.header-bar .select-all .select-text.data-v-069a0a8a {
  margin-left: 8rpx;
  font-size: 28rpx;
  color: #595959;
}
.header-bar .selected-count.data-v-069a0a8a {
  font-size: 26rpx;
  color: #8C8C8C;
}
.header-bar .batch-actions .batch-btn.data-v-069a0a8a {
  padding: 8rpx 24rpx;
  font-size: 26rpx;
  border: none;
  border-radius: 8rpx;
}
.header-bar .batch-actions .batch-btn.delete-btn.data-v-069a0a8a {
  background-color: #FF4D4F;
  color: #fff;
}
/* 收藏列表 */
.favorite-scroll.data-v-069a0a8a {
  flex: 1;
}
.favorite-list.data-v-069a0a8a {
  padding: 24rpx;
}
.favorite-list .favorite-item.data-v-069a0a8a {
  position: relative;
  display: flex;
  align-items: center;
  background-color: #fff;
  border-radius: 12rpx;
  margin-bottom: 24rpx;
  overflow: hidden;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}
.favorite-list .favorite-item .select-box.data-v-069a0a8a {
  width: 80rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}
.favorite-list .favorite-item .house-image.data-v-069a0a8a {
  width: 200rpx;
  height: 150rpx;
  flex-shrink: 0;
}
.favorite-list .favorite-item .house-info.data-v-069a0a8a {
  flex: 1;
  padding: 24rpx;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}
.favorite-list .favorite-item .house-info .house-title.data-v-069a0a8a {
  font-size: 28rpx;
  font-weight: 600;
  color: #262626;
  margin-bottom: 8rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.favorite-list .favorite-item .house-info .house-desc.data-v-069a0a8a {
  font-size: 24rpx;
  color: #8C8C8C;
  margin-bottom: 8rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.favorite-list .favorite-item .house-info .house-tags.data-v-069a0a8a {
  margin-bottom: 16rpx;
}
.favorite-list .favorite-item .house-info .house-tags .tag.data-v-069a0a8a {
  display: inline-block;
  padding: 4rpx 12rpx;
  background-color: #FAFAFA;
  color: #595959;
  font-size: 20rpx;
  border-radius: 8rpx;
  margin-right: 8rpx;
}
.favorite-list .favorite-item .house-info .house-bottom.data-v-069a0a8a {
  display: flex;
  justify-content: space-between;
  align-items: flex-end;
}
.favorite-list .favorite-item .house-info .house-bottom .price .price-num.data-v-069a0a8a {
  font-size: 28rpx;
  font-weight: 600;
  color: #FF4D4F;
}
.favorite-list .favorite-item .house-info .house-bottom .price .price-unit.data-v-069a0a8a {
  font-size: 22rpx;
  color: #8C8C8C;
}
.favorite-list .favorite-item .house-info .house-bottom .favorite-time.data-v-069a0a8a {
  font-size: 22rpx;
  color: #BFBFBF;
}
.favorite-list .favorite-item .action-btn.data-v-069a0a8a {
  width: 80rpx;
  height: 80rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}
/* 加载状态 */
.loading-more.data-v-069a0a8a {
  padding: 32rpx;
}
/* 空状态 */
.empty-state.data-v-069a0a8a {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 120rpx 32rpx;
}
.empty-state .empty-image.data-v-069a0a8a {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 32rpx;
}
.empty-state .empty-text.data-v-069a0a8a {
  font-size: 28rpx;
  color: #595959;
  margin-bottom: 16rpx;
}
.empty-state .empty-desc.data-v-069a0a8a {
  font-size: 24rpx;
  color: #8C8C8C;
  margin-bottom: 32rpx;
}
.empty-state .browse-btn.data-v-069a0a8a {
  width: 200rpx;
  height: 72rpx;
  background-color: #007AFF;
  color: #fff;
  font-size: 26rpx;
  border: none;
  border-radius: 8rpx;
}

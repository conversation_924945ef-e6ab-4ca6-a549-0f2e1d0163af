{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/web/project/前端10/pages/house/search.vue?b237", "webpack:///D:/web/project/前端10/pages/house/search.vue?19db", "webpack:///D:/web/project/前端10/pages/house/search.vue?fe0d", "webpack:///D:/web/project/前端10/pages/house/search.vue?cd67", "uni-app:///pages/house/search.vue", "webpack:///D:/web/project/前端10/pages/house/search.vue?3502", "webpack:///D:/web/project/前端10/pages/house/search.vue?7f53"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "components", "HouseCard", "data", "searchKeyword", "loading", "loadingStatus", "currentPage", "pageSize", "hasMore", "showResult", "resultCount", "searchHistory", "suggestList", "resultList", "hotSearchList", "onLoad", "watch", "methods", "loadSearchHistory", "saveSearchHistory", "history", "uni", "clearHistory", "title", "content", "success", "getSuggestList", "keyword", "onSearch", "searchByHistory", "searchByHot", "searchBySuggest", "performSearch", "loadMore", "params", "page", "limit", "status", "houseAPI", "result", "newList", "console", "clearSearch", "goBack", "goToDetail", "url", "toggleFavorite", "house", "icon"], "mappings": ";;;;;;;;;;;;;AAAA;AAE2D;AAC3D;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,eAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAA+H;AAC/H;AAC0D;AACL;AACsC;;;AAG3F;AACyK;AACzK,gBAAgB,gLAAU;AAC1B,EAAE,4EAAM;AACR,EAAE,6FAAM;AACR,EAAE,sGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,iGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,oMAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AClDA;AAAA;AAAA;AAAA;AAAioB,CAAgB,+nBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;AC8GrpB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eAGA;EACAC;IACAC;EACA;EAEAC;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MAEAC;MACAC;MACAC;MAEAC,gBACA,iCACA;IAEA;EACA;EAEAC;IACA;EACA;EAEAC;IACAb;MACA;QACA;MACA;QACA;QACA;MACA;IACA;EACA;EAEAc;IACA;IACAC;MACA;MACA;IACA;IAEA;IACAC;MACA;;MAEA;MACAC;QAAA;MAAA;;MAEA;MACAA;;MAEA;MACAA;MAEAC;MACA;IACA;IAEA;IACAC;MAAA;MACAD;QACAE;QACAC;QACAC;UACA;YACAJ;YACA;UACA;QACA;MACA;IACA;IAEA;IACAK;MACA;MACA,0BACAC,qCACAA,qCACAA,2CACAA,2CACAA,gCACA;MACA;IACA;IAEA;IACAC;MAAA;MAAA;QAAA;UAAA;YAAA;cAAA;gBAAA,IACA;kBAAA;kBAAA;gBAAA;gBAAA;cAAA;gBAEA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IAEA;IACAC;MACA;MACA;IACA;IAEA;IACAC;MACA;MACA;MACA;IACA;IAEA;IACAC;MACA;MACA;MACA;IACA;IAEA;IACAC;MAAA;QAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAAC;gBAAA;gBAEA;gBAEA;kBACA;kBACA;kBACA;gBACA;gBAEAC;kBACAP;kBACAQ;kBACAC;kBACAC;gBACA;gBAAA;gBAAA,OAEAC;cAAA;gBAAAC;gBACAC;gBAEA;kBACA;gBACA;kBACA;kBACA;gBACA;gBAEA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAGAC;gBACA;cAAA;gBAAA;gBAEA;gBAAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAR;MACA;QACA;QACA;QACA;MACA;IACA;IAEA;IACAS;MACA;MACA;MACA;IACA;IAEA;IACAC;MACAtB;IACA;IAEA;IACAuB;MACAvB;QACAwB;MACA;IACA;IAEA;IACAC;MAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA,KAEAC;kBAAA;kBAAA;gBAAA;gBAAA;gBAAA,OACAT;cAAA;gBACAS;gBACA1B;kBACAE;kBACAyB;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA,OAEAV;cAAA;gBACAS;gBACA1B;kBACAE;kBACAyB;gBACA;cAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAGAP;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACpUA;AAAA;AAAA;AAAA;AAA4tC,CAAgB,kpCAAG,EAAC,C;;;;;;;;;;;ACAhvC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/house/search.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;import '@dcloudio/vue-cli-plugin-uni/packages/uni-cloud/dist/index.js';\nimport Vue from 'vue'\nimport Page from './pages/house/search.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./search.vue?vue&type=template&id=55d1bcf0&scoped=true&\"\nvar renderjs\nimport script from \"./search.vue?vue&type=script&lang=js&\"\nexport * from \"./search.vue?vue&type=script&lang=js&\"\nimport style0 from \"./search.vue?vue&type=style&index=0&id=55d1bcf0&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"55d1bcf0\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/house/search.vue\"\nexport default component.exports", "export * from \"-!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./search.vue?vue&type=template&id=55d1bcf0&scoped=true&\"", "var components\ntry {\n  components = {\n    houseCard: function () {\n      return import(\n        /* webpackChunkName: \"components/house-card/house-card\" */ \"@/components/house-card/house-card.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 = !_vm.searchKeyword && _vm.searchHistory.length > 0\n  var g1 = _vm.searchKeyword && _vm.suggestList.length > 0\n  var g2 = _vm.showResult ? _vm.resultList.length : null\n  var g3 = _vm.showResult\n    ? !_vm.loading && _vm.resultList.length === 0 && _vm.showResult\n    : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n        g1: g1,\n        g2: g2,\n        g3: g3,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./search.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./search.vue?vue&type=script&lang=js&\"", "<template>\n  <view class=\"search-page\">\n    <!-- 搜索栏 -->\n    <view class=\"search-bar\">\n      <view class=\"search-input\">\n        <uni-icons type=\"search\" size=\"18\" color=\"#999\"></uni-icons>\n        <input \n          class=\"input\" \n          v-model=\"searchKeyword\" \n          placeholder=\"搜索房源、地址、小区\"\n          confirm-type=\"search\"\n          @confirm=\"onSearch\"\n          focus\n        />\n        <view class=\"clear-btn\" @click=\"clearSearch\" v-if=\"searchKeyword\">\n          <uni-icons type=\"clear\" size=\"16\" color=\"#999\"></uni-icons>\n        </view>\n      </view>\n      <text class=\"cancel-btn\" @click=\"goBack\">取消</text>\n    </view>\n\n    <!-- 搜索历史 -->\n    <view class=\"history-section\" v-if=\"!searchKeyword && searchHistory.length > 0\">\n      <view class=\"section-header\">\n        <text class=\"section-title\">搜索历史</text>\n        <view class=\"clear-history\" @click=\"clearHistory\">\n          <uni-icons type=\"trash\" size=\"16\" color=\"#999\"></uni-icons>\n        </view>\n      </view>\n      <view class=\"history-list\">\n        <view \n          class=\"history-item\" \n          v-for=\"(item, index) in searchHistory\" \n          :key=\"index\"\n          @click=\"searchByHistory(item)\"\n        >\n          <uni-icons type=\"clock\" size=\"14\" color=\"#999\"></uni-icons>\n          <text class=\"history-text\">{{ item }}</text>\n        </view>\n      </view>\n    </view>\n\n    <!-- 热门搜索 -->\n    <view class=\"hot-section\" v-if=\"!searchKeyword\">\n      <view class=\"section-title\">热门搜索</view>\n      <view class=\"hot-list\">\n        <view \n          class=\"hot-item\" \n          v-for=\"(item, index) in hotSearchList\" \n          :key=\"index\"\n          @click=\"searchByHot(item)\"\n        >\n          {{ item }}\n        </view>\n      </view>\n    </view>\n\n    <!-- 搜索建议 -->\n    <view class=\"suggest-section\" v-if=\"searchKeyword && suggestList.length > 0\">\n      <view class=\"suggest-list\">\n        <view \n          class=\"suggest-item\" \n          v-for=\"(item, index) in suggestList\" \n          :key=\"index\"\n          @click=\"searchBySuggest(item)\"\n        >\n          <uni-icons type=\"search\" size=\"16\" color=\"#999\"></uni-icons>\n          <text class=\"suggest-text\">{{ item }}</text>\n        </view>\n      </view>\n    </view>\n\n    <!-- 搜索结果 -->\n    <view class=\"result-section\" v-if=\"showResult\">\n      <view class=\"result-header\">\n        <text class=\"result-count\">找到 {{ resultCount }} 个房源</text>\n      </view>\n      \n      <scroll-view \n        class=\"result-scroll\" \n        scroll-y \n        @scrolltolower=\"loadMore\"\n      >\n        <view class=\"result-list\">\n          <house-card\n            v-for=\"house in resultList\"\n            :key=\"house._id\"\n            :house-data=\"house\"\n            @click=\"goToDetail\"\n            @favorite=\"toggleFavorite\"\n          ></house-card>\n        </view>\n\n        <!-- 加载状态 -->\n        <view class=\"loading-more\" v-if=\"resultList.length > 0\">\n          <uni-load-more :status=\"loadingStatus\"></uni-load-more>\n        </view>\n\n        <!-- 空状态 -->\n        <view class=\"empty-state\" v-if=\"!loading && resultList.length === 0 && showResult\">\n          <image class=\"empty-image\" src=\"/static/empty/no-search.png\"></image>\n          <text class=\"empty-text\">没有找到相关房源</text>\n          <text class=\"empty-desc\">试试其他关键词吧</text>\n        </view>\n      </scroll-view>\n    </view>\n  </view>\n</template>\n\n<script>\nimport { houseAPI } from '@/utils/request.js'\nimport HouseCard from '@/components/house-card/house-card.vue'\n\nexport default {\n  components: {\n    HouseCard\n  },\n  \n  data() {\n    return {\n      searchKeyword: '',\n      loading: false,\n      loadingStatus: 'more',\n      currentPage: 1,\n      pageSize: 10,\n      hasMore: true,\n      showResult: false,\n      resultCount: 0,\n      \n      searchHistory: [],\n      suggestList: [],\n      resultList: [],\n      \n      hotSearchList: [\n        '整租', '合租', '一居室', '二居室', '三居室',\n        '地铁房', '精装修', '拎包入住', '短租', '月租'\n      ]\n    }\n  },\n  \n  onLoad() {\n    this.loadSearchHistory()\n  },\n  \n  watch: {\n    searchKeyword(newVal) {\n      if (newVal) {\n        this.getSuggestList(newVal)\n      } else {\n        this.suggestList = []\n        this.showResult = false\n      }\n    }\n  },\n  \n  methods: {\n    // 加载搜索历史\n    loadSearchHistory() {\n      const history = uni.getStorageSync('searchHistory') || []\n      this.searchHistory = history.slice(0, 10) // 最多显示10条\n    },\n    \n    // 保存搜索历史\n    saveSearchHistory(keyword) {\n      let history = uni.getStorageSync('searchHistory') || []\n      \n      // 移除重复项\n      history = history.filter(item => item !== keyword)\n      \n      // 添加到开头\n      history.unshift(keyword)\n      \n      // 最多保存20条\n      history = history.slice(0, 20)\n      \n      uni.setStorageSync('searchHistory', history)\n      this.searchHistory = history.slice(0, 10)\n    },\n    \n    // 清除搜索历史\n    clearHistory() {\n      uni.showModal({\n        title: '提示',\n        content: '确定要清除搜索历史吗？',\n        success: (res) => {\n          if (res.confirm) {\n            uni.removeStorageSync('searchHistory')\n            this.searchHistory = []\n          }\n        }\n      })\n    },\n    \n    // 获取搜索建议\n    getSuggestList(keyword) {\n      // 模拟搜索建议\n      const suggests = [\n        `${keyword} 整租`,\n        `${keyword} 合租`,\n        `${keyword} 一居室`,\n        `${keyword} 二居室`,\n        `${keyword} 地铁房`\n      ]\n      this.suggestList = suggests.slice(0, 5)\n    },\n    \n    // 执行搜索\n    async onSearch() {\n      if (!this.searchKeyword.trim()) return\n      \n      this.saveSearchHistory(this.searchKeyword.trim())\n      this.performSearch(this.searchKeyword.trim())\n    },\n    \n    // 通过历史记录搜索\n    searchByHistory(keyword) {\n      this.searchKeyword = keyword\n      this.performSearch(keyword)\n    },\n    \n    // 通过热门搜索\n    searchByHot(keyword) {\n      this.searchKeyword = keyword\n      this.saveSearchHistory(keyword)\n      this.performSearch(keyword)\n    },\n    \n    // 通过建议搜索\n    searchBySuggest(keyword) {\n      this.searchKeyword = keyword\n      this.saveSearchHistory(keyword)\n      this.performSearch(keyword)\n    },\n    \n    // 执行搜索请求\n    async performSearch(keyword, loadMore = false) {\n      try {\n        this.loading = true\n        \n        if (!loadMore) {\n          this.currentPage = 1\n          this.hasMore = true\n          this.showResult = true\n        }\n        \n        const params = {\n          keyword,\n          page: this.currentPage,\n          limit: this.pageSize,\n          status: 'approved'\n        }\n        \n        const result = await houseAPI.getHouseList(params)\n        const newList = result.data.list || []\n        \n        if (loadMore) {\n          this.resultList.push(...newList)\n        } else {\n          this.resultList = newList\n          this.resultCount = result.data.total || newList.length\n        }\n        \n        this.hasMore = newList.length === this.pageSize\n        this.loadingStatus = this.hasMore ? 'more' : 'noMore'\n        \n      } catch (error) {\n        console.error('搜索失败：', error)\n        this.loadingStatus = 'error'\n      } finally {\n        this.loading = false\n      }\n    },\n    \n    // 加载更多\n    loadMore() {\n      if (this.hasMore && !this.loading) {\n        this.currentPage++\n        this.loadingStatus = 'loading'\n        this.performSearch(this.searchKeyword, true)\n      }\n    },\n    \n    // 清除搜索\n    clearSearch() {\n      this.searchKeyword = ''\n      this.showResult = false\n      this.suggestList = []\n    },\n    \n    // 返回\n    goBack() {\n      uni.navigateBack()\n    },\n    \n    // 跳转到详情\n    goToDetail(house) {\n      uni.navigateTo({\n        url: `/pages/house/detail?id=${house._id}`\n      })\n    },\n    \n    // 切换收藏\n    async toggleFavorite(house) {\n      try {\n        if (house.is_favorite) {\n          await houseAPI.unfavoriteHouse(house._id)\n          house.is_favorite = false\n          uni.showToast({\n            title: '已取消收藏',\n            icon: 'none'\n          })\n        } else {\n          await houseAPI.favoriteHouse(house._id)\n          house.is_favorite = true\n          uni.showToast({\n            title: '收藏成功',\n            icon: 'none'\n          })\n        }\n      } catch (error) {\n        console.error('收藏操作失败：', error)\n      }\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.search-page {\n  height: 100vh;\n  display: flex;\n  flex-direction: column;\n  background-color: $bg-color-page;\n}\n\n/* 搜索栏 */\n.search-bar {\n  display: flex;\n  align-items: center;\n  padding: $spacing-md;\n  background-color: #fff;\n  border-bottom: 1px solid $border-color-light;\n  \n  .search-input {\n    flex: 1;\n    display: flex;\n    align-items: center;\n    height: 72rpx;\n    padding: 0 $spacing-md;\n    background-color: $bg-color-light;\n    border-radius: 36rpx;\n    margin-right: $spacing-md;\n    \n    .input {\n      flex: 1;\n      margin-left: $spacing-xs;\n      font-size: 28rpx;\n      color: $text-color-primary;\n    }\n    \n    .clear-btn {\n      width: 32rpx;\n      height: 32rpx;\n      display: flex;\n      align-items: center;\n      justify-content: center;\n    }\n  }\n  \n  .cancel-btn {\n    font-size: 28rpx;\n    color: $text-color-secondary;\n  }\n}\n\n/* 搜索历史 */\n.history-section {\n  background-color: #fff;\n  padding: $spacing-lg;\n  margin-bottom: $spacing-md;\n  \n  .section-header {\n    display: flex;\n    justify-content: space-between;\n    align-items: center;\n    margin-bottom: $spacing-md;\n    \n    .section-title {\n      font-size: 28rpx;\n      font-weight: 600;\n      color: $text-color-primary;\n    }\n    \n    .clear-history {\n      width: 60rpx;\n      height: 60rpx;\n      display: flex;\n      align-items: center;\n      justify-content: center;\n    }\n  }\n  \n  .history-list {\n    .history-item {\n      display: flex;\n      align-items: center;\n      padding: $spacing-sm 0;\n      border-bottom: 1px solid $border-color-lighter;\n      \n      &:last-child {\n        border-bottom: none;\n      }\n      \n      .history-text {\n        margin-left: $spacing-sm;\n        font-size: 26rpx;\n        color: $text-color-secondary;\n      }\n    }\n  }\n}\n\n/* 热门搜索 */\n.hot-section {\n  background-color: #fff;\n  padding: $spacing-lg;\n  margin-bottom: $spacing-md;\n  \n  .section-title {\n    font-size: 28rpx;\n    font-weight: 600;\n    color: $text-color-primary;\n    margin-bottom: $spacing-md;\n  }\n  \n  .hot-list {\n    display: flex;\n    flex-wrap: wrap;\n    gap: $spacing-md;\n    \n    .hot-item {\n      padding: $spacing-sm $spacing-md;\n      background-color: $bg-color-light;\n      color: $text-color-secondary;\n      font-size: 26rpx;\n      border-radius: $border-radius-base;\n    }\n  }\n}\n\n/* 搜索建议 */\n.suggest-section {\n  background-color: #fff;\n  \n  .suggest-list {\n    .suggest-item {\n      display: flex;\n      align-items: center;\n      padding: $spacing-md $spacing-lg;\n      border-bottom: 1px solid $border-color-lighter;\n      \n      &:last-child {\n        border-bottom: none;\n      }\n      \n      .suggest-text {\n        margin-left: $spacing-sm;\n        font-size: 28rpx;\n        color: $text-color-primary;\n      }\n    }\n  }\n}\n\n/* 搜索结果 */\n.result-section {\n  flex: 1;\n  display: flex;\n  flex-direction: column;\n  \n  .result-header {\n    background-color: #fff;\n    padding: $spacing-md $spacing-lg;\n    border-bottom: 1px solid $border-color-light;\n    \n    .result-count {\n      font-size: 26rpx;\n      color: $text-color-secondary;\n    }\n  }\n  \n  .result-scroll {\n    flex: 1;\n  }\n  \n  .result-list {\n    padding: $spacing-md;\n    \n    .house-card {\n      margin-bottom: $spacing-md;\n    }\n  }\n}\n\n/* 加载状态 */\n.loading-more {\n  padding: $spacing-lg;\n}\n\n/* 空状态 */\n.empty-state {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  padding: 120rpx $spacing-lg;\n  \n  .empty-image {\n    width: 200rpx;\n    height: 200rpx;\n    margin-bottom: $spacing-lg;\n  }\n  \n  .empty-text {\n    font-size: 28rpx;\n    color: $text-color-secondary;\n    margin-bottom: $spacing-sm;\n  }\n  \n  .empty-desc {\n    font-size: 24rpx;\n    color: $text-color-tertiary;\n  }\n}\n</style>\n", "import mod from \"-!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./search.vue?vue&type=style&index=0&id=55d1bcf0&lang=scss&scoped=true&\"; export default mod; export * from \"-!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./search.vue?vue&type=style&index=0&id=55d1bcf0&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754033370464\n      var cssReload = require(\"D:/web/wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}
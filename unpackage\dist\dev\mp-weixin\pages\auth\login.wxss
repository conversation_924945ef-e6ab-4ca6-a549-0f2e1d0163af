@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 自定义项目样式变量 */
/* 主题色 */
/* 辅助色 */
/* 中性色 */
/* 背景色 */
/* 边框色 */
/* 阴影 */
/* 圆角 */
/* 间距 */
.login-page.data-v-cbd6070a {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  flex-direction: column;
  padding: 48rpx;
}
/* 顶部装饰 */
.header-section.data-v-cbd6070a {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-top: 120rpx;
  margin-bottom: 80rpx;
}
.header-section .logo.data-v-cbd6070a {
  width: 120rpx;
  height: 120rpx;
  border-radius: 24rpx;
  margin-bottom: 32rpx;
}
.header-section .app-name.data-v-cbd6070a {
  font-size: 48rpx;
  font-weight: 600;
  color: #fff;
  margin-bottom: 16rpx;
}
.header-section .slogan.data-v-cbd6070a {
  font-size: 28rpx;
  color: rgba(255, 255, 255, 0.8);
}
/* 登录区域 */
.login-section.data-v-cbd6070a {
  background-color: #fff;
  border-radius: 24rpx;
  padding: 48rpx;
  margin-bottom: 48rpx;
}
.login-section .login-title.data-v-cbd6070a {
  font-size: 36rpx;
  font-weight: 600;
  color: #262626;
  text-align: center;
  margin-bottom: 16rpx;
}
.login-section .login-desc.data-v-cbd6070a {
  font-size: 26rpx;
  color: #8C8C8C;
  text-align: center;
  margin-bottom: 48rpx;
}
.login-section .wechat-login-btn.data-v-cbd6070a {
  width: 100%;
  height: 88rpx;
  background-color: #07C160;
  color: #fff;
  font-size: 30rpx;
  border: none;
  border-radius: 8rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 32rpx;
}
.login-section .wechat-login-btn .btn-text.data-v-cbd6070a {
  margin-left: 16rpx;
}
.login-section .wechat-login-btn.data-v-cbd6070a:disabled {
  background-color: #ccc;
}
/* 手机号登录 */
.phone-login-section .divider.data-v-cbd6070a {
  display: flex;
  align-items: center;
  margin: 48rpx 0;
}
.phone-login-section .divider.data-v-cbd6070a::before, .phone-login-section .divider.data-v-cbd6070a::after {
  content: "";
  flex: 1;
  height: 1px;
  background-color: #E8E8E8;
}
.phone-login-section .divider .divider-text.data-v-cbd6070a {
  padding: 0 24rpx;
  font-size: 24rpx;
  color: #8C8C8C;
}
.phone-login-section .phone-input.data-v-cbd6070a,
.phone-login-section .code-input.data-v-cbd6070a {
  margin-bottom: 32rpx;
}
.phone-login-section .phone-field.data-v-cbd6070a,
.phone-login-section .code-field.data-v-cbd6070a {
  width: 100%;
  height: 80rpx;
  padding: 0 24rpx;
  background-color: #FAFAFA;
  border: 1px solid #E8E8E8;
  border-radius: 8rpx;
  font-size: 28rpx;
}
.phone-login-section .phone-field.data-v-cbd6070a::-webkit-input-placeholder, .phone-login-section .code-field.data-v-cbd6070a::-webkit-input-placeholder {
  color: #BFBFBF;
}
.phone-login-section .phone-field.data-v-cbd6070a::placeholder,
.phone-login-section .code-field.data-v-cbd6070a::placeholder {
  color: #BFBFBF;
}
.phone-login-section .code-input.data-v-cbd6070a {
  display: flex;
  gap: 24rpx;
}
.phone-login-section .code-input .code-field.data-v-cbd6070a {
  flex: 1;
}
.phone-login-section .code-input .send-code-btn.data-v-cbd6070a {
  width: 200rpx;
  height: 80rpx;
  background-color: #007AFF;
  color: #fff;
  font-size: 24rpx;
  border: none;
  border-radius: 8rpx;
}
.phone-login-section .code-input .send-code-btn.data-v-cbd6070a:disabled {
  background-color: #BFBFBF;
}
.phone-login-section .phone-login-btn.data-v-cbd6070a {
  width: 100%;
  height: 88rpx;
  background-color: #007AFF;
  color: #fff;
  font-size: 30rpx;
  border: none;
  border-radius: 8rpx;
}
.phone-login-section .phone-login-btn.data-v-cbd6070a:disabled {
  background-color: #BFBFBF;
}
/* 协议条款 */
.agreement-section .agreement-text.data-v-cbd6070a {
  text-align: center;
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.8);
}
.agreement-section .agreement-text .link.data-v-cbd6070a {
  color: #fff;
  text-decoration: underline;
}
/* 底部装饰 */
.footer-section.data-v-cbd6070a {
  flex: 1;
  display: flex;
  align-items: flex-end;
  justify-content: center;
  padding-bottom: 48rpx;
}
.footer-section .footer-text.data-v-cbd6070a {
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.6);
}

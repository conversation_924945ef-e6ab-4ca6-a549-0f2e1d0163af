@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 自定义项目样式变量 */
/* 主题色 */
/* 辅助色 */
/* 中性色 */
/* 背景色 */
/* 边框色 */
/* 阴影 */
/* 圆角 */
/* 间距 */
.house-list-page.data-v-145608e6 {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: #F5F5F5;
}
/* 筛选栏 */
.filter-bar.data-v-145608e6 {
  display: flex;
  background-color: #fff;
  border-bottom: 1px solid #E8E8E8;
}
.filter-bar .filter-item.data-v-145608e6 {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  height: 88rpx;
}
.filter-bar .filter-item .filter-text.data-v-145608e6 {
  font-size: 28rpx;
  color: #595959;
  margin-right: 8rpx;
}
.filter-bar .filter-item.data-v-145608e6:not(:last-child) {
  border-right: 1px solid #E8E8E8;
}
/* 房源列表 */
.house-scroll.data-v-145608e6 {
  flex: 1;
}
.house-list.data-v-145608e6 {
  padding: 24rpx;
}
.house-list .house-item.data-v-145608e6 {
  position: relative;
  display: flex;
  background-color: #fff;
  border-radius: 12rpx;
  margin-bottom: 24rpx;
  overflow: hidden;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}
.house-list .house-item .house-image.data-v-145608e6 {
  width: 240rpx;
  height: 180rpx;
  flex-shrink: 0;
}
.house-list .house-item .house-info.data-v-145608e6 {
  flex: 1;
  padding: 24rpx;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}
.house-list .house-item .house-info .house-title.data-v-145608e6 {
  font-size: 30rpx;
  font-weight: 600;
  color: #262626;
  margin-bottom: 8rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  padding-right: 60rpx;
}
.house-list .house-item .house-info .house-desc.data-v-145608e6 {
  font-size: 24rpx;
  color: #8C8C8C;
  margin-bottom: 8rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.house-list .house-item .house-info .house-tags.data-v-145608e6 {
  margin-bottom: 16rpx;
}
.house-list .house-item .house-info .house-tags .tag.data-v-145608e6 {
  display: inline-block;
  padding: 4rpx 12rpx;
  background-color: #FAFAFA;
  color: #595959;
  font-size: 20rpx;
  border-radius: 8rpx;
  margin-right: 8rpx;
}
.house-list .house-item .house-info .house-bottom.data-v-145608e6 {
  display: flex;
  justify-content: space-between;
  align-items: flex-end;
}
.house-list .house-item .house-info .house-bottom .price .price-num.data-v-145608e6 {
  font-size: 32rpx;
  font-weight: 600;
  color: #FF4D4F;
}
.house-list .house-item .house-info .house-bottom .price .price-unit.data-v-145608e6 {
  font-size: 24rpx;
  color: #8C8C8C;
}
.house-list .house-item .house-info .house-bottom .location.data-v-145608e6 {
  display: flex;
  align-items: center;
}
.house-list .house-item .house-info .house-bottom .location .location-text.data-v-145608e6 {
  margin-left: 4rpx;
  font-size: 22rpx;
  color: #8C8C8C;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  max-width: 160rpx;
}
.house-list .house-item .favorite-btn.data-v-145608e6 {
  position: absolute;
  top: 24rpx;
  right: 24rpx;
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: rgba(255, 255, 255, 0.9);
  border-radius: 50%;
}
/* 加载状态 */
.loading-more.data-v-145608e6 {
  padding: 32rpx;
}
/* 空状态 */
.empty-state.data-v-145608e6 {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 120rpx 32rpx;
}
.empty-state .empty-image.data-v-145608e6 {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 32rpx;
}
.empty-state .empty-text.data-v-145608e6 {
  font-size: 28rpx;
  color: #8C8C8C;
}
/* 筛选弹窗 */
.filter-popup.data-v-145608e6 {
  background-color: #fff;
  border-radius: 24rpx 24rpx 0 0;
  max-height: 80vh;
}
.filter-popup .popup-header.data-v-145608e6 {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 32rpx;
  border-bottom: 1px solid #E8E8E8;
}
.filter-popup .popup-header .popup-title.data-v-145608e6 {
  font-size: 32rpx;
  font-weight: 600;
  color: #262626;
}
.filter-popup .popup-header .popup-close.data-v-145608e6 {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}
.filter-popup .popup-content.data-v-145608e6 {
  padding: 32rpx;
  max-height: 60vh;
  overflow-y: auto;
}
.filter-popup .popup-content .filter-group .group-title.data-v-145608e6 {
  font-size: 28rpx;
  font-weight: 600;
  color: #262626;
  margin-bottom: 24rpx;
}
.filter-popup .popup-content .filter-group .option-list.data-v-145608e6 {
  display: flex;
  flex-wrap: wrap;
  gap: 24rpx;
}
.filter-popup .popup-content .filter-group .option-list .option-item.data-v-145608e6 {
  padding: 16rpx 24rpx;
  background-color: #FAFAFA;
  color: #595959;
  font-size: 26rpx;
  border-radius: 8rpx;
  border: 1px solid transparent;
}
.filter-popup .popup-content .filter-group .option-list .option-item.active.data-v-145608e6 {
  background-color: rgba(0, 122, 255, 0.1);
  color: #007AFF;
  border-color: #007AFF;
}
.filter-popup .popup-footer.data-v-145608e6 {
  display: flex;
  padding: 32rpx;
  border-top: 1px solid #E8E8E8;
  gap: 24rpx;
}
.filter-popup .popup-footer .reset-btn.data-v-145608e6 {
  flex: 1;
  height: 80rpx;
  background-color: #FAFAFA;
  color: #595959;
  border: none;
  border-radius: 8rpx;
  font-size: 28rpx;
}
.filter-popup .popup-footer .confirm-btn.data-v-145608e6 {
  flex: 2;
  height: 80rpx;
  background-color: #007AFF;
  color: #fff;
  border: none;
  border-radius: 8rpx;
  font-size: 28rpx;
}

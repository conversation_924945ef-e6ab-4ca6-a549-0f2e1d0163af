<template>
  <view class="house-list-page">
    <!-- 筛选栏 -->
    <view class="filter-bar">
      <view class="filter-item" @click="showAreaFilter">
        <text class="filter-text">{{ selectedArea || '区域' }}</text>
        <uni-icons type="down" size="12" color="#999"></uni-icons>
      </view>
      <view class="filter-item" @click="showPriceFilter">
        <text class="filter-text">{{ selectedPrice || '价格' }}</text>
        <uni-icons type="down" size="12" color="#999"></uni-icons>
      </view>
      <view class="filter-item" @click="showTypeFilter">
        <text class="filter-text">{{ selectedType || '类型' }}</text>
        <uni-icons type="down" size="12" color="#999"></uni-icons>
      </view>
      <view class="filter-item" @click="showSortFilter">
        <text class="filter-text">{{ selectedSort || '排序' }}</text>
        <uni-icons type="down" size="12" color="#999"></uni-icons>
      </view>
    </view>

    <!-- 房源列表 -->
    <scroll-view 
      class="house-scroll" 
      scroll-y 
      @scrolltolower="loadMore"
      refresher-enabled
      @refresherrefresh="onRefresh"
      :refresher-triggered="refreshing"
    >
      <view class="house-list">
        <view 
          class="house-item" 
          v-for="house in houseList" 
          :key="house._id"
          @click="goToDetail(house._id)"
        >
          <image class="house-image" :src="house.images[0]" mode="aspectFill"></image>
          <view class="house-info">
            <view class="house-title">{{ house.title }}</view>
            <view class="house-desc">{{ house.description }}</view>
            <view class="house-tags">
              <text class="tag" v-for="tag in house.tags" :key="tag">{{ tag }}</text>
            </view>
            <view class="house-bottom">
              <view class="price">
                <text class="price-num">{{ house.price }}</text>
                <text class="price-unit">元/月</text>
              </view>
              <view class="location">
                <uni-icons type="location" size="12" color="#999"></uni-icons>
                <text class="location-text">{{ house.address }}</text>
              </view>
            </view>
          </view>
          <view class="favorite-btn" @click.stop="toggleFavorite(house)">
            <uni-icons 
              :type="house.is_favorite ? 'heart-filled' : 'heart'" 
              size="20" 
              :color="house.is_favorite ? '#FF4D4F' : '#999'"
            ></uni-icons>
          </view>
        </view>
      </view>

      <!-- 加载状态 -->
      <view class="loading-more" v-if="houseList.length > 0">
        <uni-load-more :status="loadingStatus"></uni-load-more>
      </view>

      <!-- 空状态 -->
      <view class="empty-state" v-if="!loading && houseList.length === 0">
        <image class="empty-image" src="/static/empty/no-house.png"></image>
        <text class="empty-text">暂无房源信息</text>
      </view>
    </scroll-view>

    <!-- 筛选弹窗 -->
    <uni-popup ref="filterPopup" type="bottom">
      <view class="filter-popup">
        <view class="popup-header">
          <text class="popup-title">筛选条件</text>
          <view class="popup-close" @click="closeFilter">
            <uni-icons type="close" size="18" color="#999"></uni-icons>
          </view>
        </view>
        <view class="popup-content">
          <!-- 动态筛选内容 -->
          <view v-if="currentFilter === 'area'">
            <view class="filter-group">
              <text class="group-title">选择区域</text>
              <view class="option-list">
                <view 
                  class="option-item" 
                  v-for="area in areaOptions" 
                  :key="area.value"
                  :class="{ active: selectedArea === area.label }"
                  @click="selectArea(area)"
                >
                  {{ area.label }}
                </view>
              </view>
            </view>
          </view>
          
          <view v-if="currentFilter === 'price'">
            <view class="filter-group">
              <text class="group-title">价格范围</text>
              <view class="option-list">
                <view 
                  class="option-item" 
                  v-for="price in priceOptions" 
                  :key="price.value"
                  :class="{ active: selectedPrice === price.label }"
                  @click="selectPrice(price)"
                >
                  {{ price.label }}
                </view>
              </view>
            </view>
          </view>
          
          <view v-if="currentFilter === 'type'">
            <view class="filter-group">
              <text class="group-title">房源类型</text>
              <view class="option-list">
                <view 
                  class="option-item" 
                  v-for="type in typeOptions" 
                  :key="type.value"
                  :class="{ active: selectedType === type.label }"
                  @click="selectType(type)"
                >
                  {{ type.label }}
                </view>
              </view>
            </view>
          </view>
          
          <view v-if="currentFilter === 'sort'">
            <view class="filter-group">
              <text class="group-title">排序方式</text>
              <view class="option-list">
                <view 
                  class="option-item" 
                  v-for="sort in sortOptions" 
                  :key="sort.value"
                  :class="{ active: selectedSort === sort.label }"
                  @click="selectSort(sort)"
                >
                  {{ sort.label }}
                </view>
              </view>
            </view>
          </view>
        </view>
        <view class="popup-footer">
          <button class="reset-btn" @click="resetFilter">重置</button>
          <button class="confirm-btn" @click="confirmFilter">确定</button>
        </view>
      </view>
    </uni-popup>
  </view>
</template>

<script>
import { houseAPI } from '@/utils/request.js'

export default {
  data() {
    return {
      loading: false,
      refreshing: false,
      loadingStatus: 'more',
      currentPage: 1,
      pageSize: 10,
      hasMore: true,
      
      houseList: [],
      
      // 筛选相关
      currentFilter: '',
      selectedArea: '',
      selectedPrice: '',
      selectedType: '',
      selectedSort: '',
      
      // 筛选选项
      areaOptions: [
        { label: '不限', value: '' },
        { label: '朝阳区', value: 'chaoyang' },
        { label: '海淀区', value: 'haidian' },
        { label: '西城区', value: 'xicheng' },
        { label: '东城区', value: 'dongcheng' },
        { label: '丰台区', value: 'fengtai' }
      ],
      priceOptions: [
        { label: '不限', value: '' },
        { label: '1000以下', value: '0-1000' },
        { label: '1000-2000', value: '1000-2000' },
        { label: '2000-3000', value: '2000-3000' },
        { label: '3000-5000', value: '3000-5000' },
        { label: '5000以上', value: '5000-' }
      ],
      typeOptions: [
        { label: '不限', value: '' },
        { label: '整租', value: 'whole' },
        { label: '合租', value: 'shared' },
        { label: '短租', value: 'short' }
      ],
      sortOptions: [
        { label: '默认排序', value: '' },
        { label: '价格从低到高', value: 'price_asc' },
        { label: '价格从高到低', value: 'price_desc' },
        { label: '最新发布', value: 'time_desc' }
      ]
    }
  },
  
  onLoad(options) {
    // 处理从首页传来的类型参数
    if (options.type) {
      this.selectedType = this.getTypeName(options.type)
    }
    this.loadHouseList()
  },
  
  methods: {
    // 加载房源列表
    async loadHouseList(refresh = false) {
      if (this.loading) return
      
      try {
        this.loading = true
        if (refresh) {
          this.currentPage = 1
          this.hasMore = true
          this.refreshing = true
        }
        
        const params = {
          page: this.currentPage,
          limit: this.pageSize,
          status: 'approved'
        }
        
        // 添加筛选条件
        if (this.selectedArea && this.selectedArea !== '不限') {
          params.area = this.getAreaValue(this.selectedArea)
        }
        if (this.selectedPrice && this.selectedPrice !== '不限') {
          params.price_range = this.getPriceValue(this.selectedPrice)
        }
        if (this.selectedType && this.selectedType !== '不限') {
          params.type = this.getTypeValue(this.selectedType)
        }
        if (this.selectedSort && this.selectedSort !== '默认排序') {
          params.sort = this.getSortValue(this.selectedSort)
        }
        
        const result = await houseAPI.getHouseList(params)
        const newList = result.data.list || []
        
        if (refresh) {
          this.houseList = newList
        } else {
          this.houseList.push(...newList)
        }
        
        this.hasMore = newList.length === this.pageSize
        this.loadingStatus = this.hasMore ? 'more' : 'noMore'
        
      } catch (error) {
        console.error('加载房源列表失败：', error)
        this.loadingStatus = 'error'
      } finally {
        this.loading = false
        this.refreshing = false
      }
    },
    
    // 加载更多
    loadMore() {
      if (this.hasMore && !this.loading) {
        this.currentPage++
        this.loadingStatus = 'loading'
        this.loadHouseList()
      }
    },
    
    // 下拉刷新
    onRefresh() {
      this.loadHouseList(true)
    },
    
    // 跳转到详情页
    goToDetail(houseId) {
      uni.navigateTo({
        url: `/pages/house/detail?id=${houseId}`
      })
    },
    
    // 切换收藏状态
    async toggleFavorite(house) {
      try {
        if (house.is_favorite) {
          await houseAPI.unfavoriteHouse(house._id)
          house.is_favorite = false
          uni.showToast({
            title: '已取消收藏',
            icon: 'none'
          })
        } else {
          await houseAPI.favoriteHouse(house._id)
          house.is_favorite = true
          uni.showToast({
            title: '收藏成功',
            icon: 'none'
          })
        }
      } catch (error) {
        console.error('收藏操作失败：', error)
      }
    },
    
    // 显示筛选弹窗
    showAreaFilter() {
      this.currentFilter = 'area'
      this.$refs.filterPopup.open()
    },
    
    showPriceFilter() {
      this.currentFilter = 'price'
      this.$refs.filterPopup.open()
    },
    
    showTypeFilter() {
      this.currentFilter = 'type'
      this.$refs.filterPopup.open()
    },
    
    showSortFilter() {
      this.currentFilter = 'sort'
      this.$refs.filterPopup.open()
    },
    
    // 关闭筛选弹窗
    closeFilter() {
      this.$refs.filterPopup.close()
    },
    
    // 选择筛选项
    selectArea(area) {
      this.selectedArea = area.label
      this.closeFilter()
      this.loadHouseList(true)
    },
    
    selectPrice(price) {
      this.selectedPrice = price.label
      this.closeFilter()
      this.loadHouseList(true)
    },
    
    selectType(type) {
      this.selectedType = type.label
      this.closeFilter()
      this.loadHouseList(true)
    },
    
    selectSort(sort) {
      this.selectedSort = sort.label
      this.closeFilter()
      this.loadHouseList(true)
    },
    
    // 重置筛选
    resetFilter() {
      this.selectedArea = ''
      this.selectedPrice = ''
      this.selectedType = ''
      this.selectedSort = ''
      this.closeFilter()
      this.loadHouseList(true)
    },
    
    // 确认筛选
    confirmFilter() {
      this.closeFilter()
      this.loadHouseList(true)
    },
    
    // 辅助方法
    getTypeName(type) {
      const typeMap = {
        'whole': '整租',
        'shared': '合租',
        'short': '短租'
      }
      return typeMap[type] || ''
    },
    
    getAreaValue(label) {
      const area = this.areaOptions.find(item => item.label === label)
      return area ? area.value : ''
    },
    
    getPriceValue(label) {
      const price = this.priceOptions.find(item => item.label === label)
      return price ? price.value : ''
    },
    
    getTypeValue(label) {
      const type = this.typeOptions.find(item => item.label === label)
      return type ? type.value : ''
    },
    
    getSortValue(label) {
      const sort = this.sortOptions.find(item => item.label === label)
      return sort ? sort.value : ''
    }
  }
}
</script>

<style lang="scss" scoped>
.house-list-page {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: $bg-color-page;
}

/* 筛选栏 */
.filter-bar {
  display: flex;
  background-color: #fff;
  border-bottom: 1px solid $border-color-light;

  .filter-item {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    height: 88rpx;

    .filter-text {
      font-size: 28rpx;
      color: $text-color-secondary;
      margin-right: 8rpx;
    }

    &:not(:last-child) {
      border-right: 1px solid $border-color-light;
    }
  }
}

/* 房源列表 */
.house-scroll {
  flex: 1;
}

.house-list {
  padding: $spacing-md;

  .house-item {
    position: relative;
    display: flex;
    background-color: #fff;
    border-radius: $border-radius-large;
    margin-bottom: $spacing-md;
    overflow: hidden;
    box-shadow: $box-shadow-light;

    .house-image {
      width: 240rpx;
      height: 180rpx;
      flex-shrink: 0;
    }

    .house-info {
      flex: 1;
      padding: $spacing-md;
      display: flex;
      flex-direction: column;
      justify-content: space-between;

      .house-title {
        font-size: 30rpx;
        font-weight: 600;
        color: $text-color-primary;
        margin-bottom: $spacing-xs;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        padding-right: 60rpx;
      }

      .house-desc {
        font-size: 24rpx;
        color: $text-color-tertiary;
        margin-bottom: $spacing-xs;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }

      .house-tags {
        margin-bottom: $spacing-sm;

        .tag {
          display: inline-block;
          padding: 4rpx 12rpx;
          background-color: $bg-color-light;
          color: $text-color-secondary;
          font-size: 20rpx;
          border-radius: 8rpx;
          margin-right: $spacing-xs;
        }
      }

      .house-bottom {
        display: flex;
        justify-content: space-between;
        align-items: flex-end;

        .price {
          .price-num {
            font-size: 32rpx;
            font-weight: 600;
            color: $error-color;
          }

          .price-unit {
            font-size: 24rpx;
            color: $text-color-tertiary;
          }
        }

        .location {
          display: flex;
          align-items: center;

          .location-text {
            margin-left: 4rpx;
            font-size: 22rpx;
            color: $text-color-tertiary;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            max-width: 160rpx;
          }
        }
      }
    }

    .favorite-btn {
      position: absolute;
      top: $spacing-md;
      right: $spacing-md;
      width: 60rpx;
      height: 60rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      background-color: rgba(255, 255, 255, 0.9);
      border-radius: 50%;
    }
  }
}

/* 加载状态 */
.loading-more {
  padding: $spacing-lg;
}

/* 空状态 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 120rpx $spacing-lg;

  .empty-image {
    width: 200rpx;
    height: 200rpx;
    margin-bottom: $spacing-lg;
  }

  .empty-text {
    font-size: 28rpx;
    color: $text-color-tertiary;
  }
}

/* 筛选弹窗 */
.filter-popup {
  background-color: #fff;
  border-radius: 24rpx 24rpx 0 0;
  max-height: 80vh;

  .popup-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: $spacing-lg;
    border-bottom: 1px solid $border-color-light;

    .popup-title {
      font-size: 32rpx;
      font-weight: 600;
      color: $text-color-primary;
    }

    .popup-close {
      width: 60rpx;
      height: 60rpx;
      display: flex;
      align-items: center;
      justify-content: center;
    }
  }

  .popup-content {
    padding: $spacing-lg;
    max-height: 60vh;
    overflow-y: auto;

    .filter-group {
      .group-title {
        font-size: 28rpx;
        font-weight: 600;
        color: $text-color-primary;
        margin-bottom: $spacing-md;
      }

      .option-list {
        display: flex;
        flex-wrap: wrap;
        gap: $spacing-md;

        .option-item {
          padding: $spacing-sm $spacing-md;
          background-color: $bg-color-light;
          color: $text-color-secondary;
          font-size: 26rpx;
          border-radius: $border-radius-base;
          border: 1px solid transparent;

          &.active {
            background-color: rgba($primary-color, 0.1);
            color: $primary-color;
            border-color: $primary-color;
          }
        }
      }
    }
  }

  .popup-footer {
    display: flex;
    padding: $spacing-lg;
    border-top: 1px solid $border-color-light;
    gap: $spacing-md;

    .reset-btn {
      flex: 1;
      height: 80rpx;
      background-color: $bg-color-light;
      color: $text-color-secondary;
      border: none;
      border-radius: $border-radius-base;
      font-size: 28rpx;
    }

    .confirm-btn {
      flex: 2;
      height: 80rpx;
      background-color: $primary-color;
      color: #fff;
      border: none;
      border-radius: $border-radius-base;
      font-size: 28rpx;
    }
  }
}
</style>

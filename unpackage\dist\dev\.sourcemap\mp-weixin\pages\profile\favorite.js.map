{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/web/project/前端10/pages/profile/favorite.vue?7f0e", "webpack:///D:/web/project/前端10/pages/profile/favorite.vue?63e0", "webpack:///D:/web/project/前端10/pages/profile/favorite.vue?cc5f", "webpack:///D:/web/project/前端10/pages/profile/favorite.vue?c3f6", "uni-app:///pages/profile/favorite.vue", "webpack:///D:/web/project/前端10/pages/profile/favorite.vue?da0a", "webpack:///D:/web/project/前端10/pages/profile/favorite.vue?af30"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "loading", "refreshing", "loadingStatus", "currentPage", "pageSize", "hasMore", "favoriteList", "selectedList", "computed", "isAllSelected", "onLoad", "methods", "loadFavorites", "refresh", "params", "page", "limit", "houseAPI", "result", "newList", "console", "loadMore", "onRefresh", "toggleSelect", "toggleSelectAll", "batchDelete", "uni", "title", "content", "success", "res", "houseId", "icon", "removeFavorite", "index", "selectedIndex", "goToDetail", "url", "goToHome", "formatTime"], "mappings": ";;;;;;;;;;;;;AAAA;AAE2D;AAC3D;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,iBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAiI;AACjI;AAC4D;AACL;AACsC;;;AAG7F;AACyK;AACzK,gBAAgB,gLAAU;AAC1B,EAAE,8EAAM;AACR,EAAE,+FAAM;AACR,EAAE,wGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,mGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AC5CA;AAAA;AAAA;AAAA;AAAmoB,CAAgB,ioBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;ACsFvpB;AAAA;AAAA;AAAA;AAAA,eAEA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MAEAC;MACAC;IACA;EACA;EAEAC;IACAC;MACA;IACA;EACA;EAEAC;IACA;EACA;EAEAC;IACA;IACAC;MAAA;QAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAAC;gBAAA,KACA;kBAAA;kBAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAGA;gBACA;kBACA;kBACA;kBACA;kBACA;gBACA;gBAEAC;kBACAC;kBACAC;gBACA;gBAAA;gBAAA,OAEAC;cAAA;gBAAAC;gBACAC;gBAEA;kBACA;gBACA;kBACA;gBACA;gBAEA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAGAC;gBACA;cAAA;gBAAA;gBAEA;gBACA;gBAAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAC;MACA;QACA;QACA;QACA;MACA;IACA;IAEA;IACAC;MACA;IACA;IAEA;IACAC;MACA;MACA;QACA;MACA;QACA;MACA;IACA;IAEA;IACAC;MACA;QACA;MACA;QACA;UAAA;QAAA;MACA;IACA;IAEA;IACAC;MAAA;MACA;MAEAC;QACAC;QACAC;QACAC;UAAA;YAAA;YAAA;cAAA;gBAAA;kBAAA;oBAAA,KACAC;sBAAA;sBAAA;oBAAA;oBAAA;oBAEA;oBAAA,uCACA;oBAAA;oBAAA;kBAAA;oBAAA;sBAAA;sBAAA;oBAAA;oBAAAC;oBAAA;oBAAA,OACAd;kBAAA;oBAAA;oBAAA;kBAAA;oBAAA;oBAAA;kBAAA;oBAAA;oBAAA;oBAAA;kBAAA;oBAAA;oBAAA;oBAAA;kBAAA;oBAGA;oBACA,iDACA;sBAAA;oBAAA,EACA;oBAEA;oBAEAS;sBACAC;sBACAK;oBACA;oBAAA;oBAAA;kBAAA;oBAAA;oBAAA;oBAGAZ;oBACAM;sBACAC;sBACAK;oBACA;kBAAA;kBAAA;oBAAA;gBAAA;cAAA;YAAA;UAAA,CAGA;UAAA;YAAA;UAAA;UAAA;QAAA;MACA;IACA;IAEA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA;gBAAA,OAEAhB;cAAA;gBAEA;gBACAiB;kBAAA;gBAAA;gBACA;kBACA;gBACA;;gBAEA;gBACAC;gBACA;kBACA;gBACA;gBAEAT;kBACAC;kBACAK;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAGAZ;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAgB;MACAV;QACAW;MACA;IACA;IAEA;IACAC;MACAZ;QACAW;MACA;IACA;IAEA;IACAE;MACA;MAEA;MACA;MACA;MAEA;QAAA;QACA;MACA;QAAA;QACA;MACA;QAAA;QACA;MACA;QAAA;QACA;MACA;QACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AC/RA;AAAA;AAAA;AAAA;AAA8tC,CAAgB,opCAAG,EAAC,C;;;;;;;;;;;ACAlvC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/profile/favorite.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;import '@dcloudio/vue-cli-plugin-uni/packages/uni-cloud/dist/index.js';\nimport Vue from 'vue'\nimport Page from './pages/profile/favorite.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./favorite.vue?vue&type=template&id=069a0a8a&scoped=true&\"\nvar renderjs\nimport script from \"./favorite.vue?vue&type=script&lang=js&\"\nexport * from \"./favorite.vue?vue&type=script&lang=js&\"\nimport style0 from \"./favorite.vue?vue&type=style&index=0&id=069a0a8a&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"069a0a8a\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/profile/favorite.vue\"\nexport default component.exports", "export * from \"-!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./favorite.vue?vue&type=template&id=069a0a8a&scoped=true&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 = _vm.favoriteList.length\n  var g1 = g0 > 0 ? _vm.selectedList.length : null\n  var g2 = g0 > 0 && g1 > 0 ? _vm.selectedList.length : null\n  var g3 = g0 > 0 ? _vm.selectedList.length : null\n  var l1 = _vm.__map(_vm.favoriteList, function (house, __i0__) {\n    var $orig = _vm.__get_orig(house)\n    var g4 = _vm.selectedList.includes(house._id)\n    var g5 = _vm.selectedList.includes(house._id)\n    var g6 = house.tags && house.tags.length > 0\n    var l0 = g6 ? house.tags.slice(0, 3) : null\n    var m0 = _vm.formatTime(house.favorite_time)\n    return {\n      $orig: $orig,\n      g4: g4,\n      g5: g5,\n      g6: g6,\n      l0: l0,\n      m0: m0,\n    }\n  })\n  var g7 = _vm.favoriteList.length\n  var g8 = !_vm.loading && _vm.favoriteList.length === 0\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n        g1: g1,\n        g2: g2,\n        g3: g3,\n        l1: l1,\n        g7: g7,\n        g8: g8,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./favorite.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./favorite.vue?vue&type=script&lang=js&\"", "<template>\n  <view class=\"favorite-page\">\n    <!-- 顶部操作栏 -->\n    <view class=\"header-bar\" v-if=\"favoriteList.length > 0\">\n      <view class=\"select-all\" @click=\"toggleSelectAll\">\n        <uni-icons \n          :type=\"isAllSelected ? 'checkbox-filled' : 'checkbox'\" \n          size=\"18\" \n          :color=\"isAllSelected ? '#007AFF' : '#999'\"\n        ></uni-icons>\n        <text class=\"select-text\">全选</text>\n      </view>\n      <view class=\"selected-count\" v-if=\"selectedList.length > 0\">\n        已选择 {{ selectedList.length }} 个\n      </view>\n      <view class=\"batch-actions\" v-if=\"selectedList.length > 0\">\n        <button class=\"batch-btn delete-btn\" @click=\"batchDelete\">删除</button>\n      </view>\n    </view>\n\n    <!-- 收藏列表 -->\n    <scroll-view \n      class=\"favorite-scroll\" \n      scroll-y \n      @scrolltolower=\"loadMore\"\n      refresher-enabled\n      @refresherrefresh=\"onRefresh\"\n      :refresher-triggered=\"refreshing\"\n    >\n      <view class=\"favorite-list\">\n        <view \n          class=\"favorite-item\" \n          v-for=\"house in favoriteList\" \n          :key=\"house._id\"\n          @click=\"goToDetail(house)\"\n        >\n          <view class=\"select-box\" @click.stop=\"toggleSelect(house)\">\n            <uni-icons \n              :type=\"selectedList.includes(house._id) ? 'checkbox-filled' : 'checkbox'\" \n              size=\"18\" \n              :color=\"selectedList.includes(house._id) ? '#007AFF' : '#999'\"\n            ></uni-icons>\n          </view>\n          \n          <image class=\"house-image\" :src=\"house.images[0]\" mode=\"aspectFill\"></image>\n          \n          <view class=\"house-info\">\n            <view class=\"house-title\">{{ house.title }}</view>\n            <view class=\"house-desc\">{{ house.description }}</view>\n            <view class=\"house-tags\" v-if=\"house.tags && house.tags.length > 0\">\n              <text class=\"tag\" v-for=\"tag in house.tags.slice(0, 3)\" :key=\"tag\">{{ tag }}</text>\n            </view>\n            <view class=\"house-bottom\">\n              <view class=\"price\">\n                <text class=\"price-num\">{{ house.price }}</text>\n                <text class=\"price-unit\">元/月</text>\n              </view>\n              <view class=\"favorite-time\">\n                {{ formatTime(house.favorite_time) }}\n              </view>\n            </view>\n          </view>\n          \n          <view class=\"action-btn\" @click.stop=\"removeFavorite(house)\">\n            <uni-icons type=\"heart-filled\" size=\"20\" color=\"#FF4D4F\"></uni-icons>\n          </view>\n        </view>\n      </view>\n\n      <!-- 加载状态 -->\n      <view class=\"loading-more\" v-if=\"favoriteList.length > 0\">\n        <uni-load-more :status=\"loadingStatus\"></uni-load-more>\n      </view>\n\n      <!-- 空状态 -->\n      <view class=\"empty-state\" v-if=\"!loading && favoriteList.length === 0\">\n        <image class=\"empty-image\" src=\"/static/empty/no-favorite.png\"></image>\n        <text class=\"empty-text\">还没有收藏过房源</text>\n        <text class=\"empty-desc\">去首页看看有什么好房源吧</text>\n        <button class=\"browse-btn\" @click=\"goToHome\">去逛逛</button>\n      </view>\n    </scroll-view>\n  </view>\n</template>\n\n<script>\nimport { houseAPI } from '@/utils/request.js'\n\nexport default {\n  data() {\n    return {\n      loading: false,\n      refreshing: false,\n      loadingStatus: 'more',\n      currentPage: 1,\n      pageSize: 10,\n      hasMore: true,\n      \n      favoriteList: [],\n      selectedList: []\n    }\n  },\n  \n  computed: {\n    isAllSelected() {\n      return this.favoriteList.length > 0 && this.selectedList.length === this.favoriteList.length\n    }\n  },\n  \n  onLoad() {\n    this.loadFavorites()\n  },\n  \n  methods: {\n    // 加载收藏列表\n    async loadFavorites(refresh = false) {\n      if (this.loading) return\n      \n      try {\n        this.loading = true\n        if (refresh) {\n          this.currentPage = 1\n          this.hasMore = true\n          this.refreshing = true\n          this.selectedList = []\n        }\n        \n        const params = {\n          page: this.currentPage,\n          limit: this.pageSize\n        }\n        \n        const result = await houseAPI.getMyFavorites(params)\n        const newList = result.data.list || []\n        \n        if (refresh) {\n          this.favoriteList = newList\n        } else {\n          this.favoriteList.push(...newList)\n        }\n        \n        this.hasMore = newList.length === this.pageSize\n        this.loadingStatus = this.hasMore ? 'more' : 'noMore'\n        \n      } catch (error) {\n        console.error('加载收藏列表失败：', error)\n        this.loadingStatus = 'error'\n      } finally {\n        this.loading = false\n        this.refreshing = false\n      }\n    },\n    \n    // 加载更多\n    loadMore() {\n      if (this.hasMore && !this.loading) {\n        this.currentPage++\n        this.loadingStatus = 'loading'\n        this.loadFavorites()\n      }\n    },\n    \n    // 下拉刷新\n    onRefresh() {\n      this.loadFavorites(true)\n    },\n    \n    // 切换选择\n    toggleSelect(house) {\n      const index = this.selectedList.indexOf(house._id)\n      if (index > -1) {\n        this.selectedList.splice(index, 1)\n      } else {\n        this.selectedList.push(house._id)\n      }\n    },\n    \n    // 全选/取消全选\n    toggleSelectAll() {\n      if (this.isAllSelected) {\n        this.selectedList = []\n      } else {\n        this.selectedList = this.favoriteList.map(item => item._id)\n      }\n    },\n    \n    // 批量删除\n    batchDelete() {\n      if (this.selectedList.length === 0) return\n      \n      uni.showModal({\n        title: '确认删除',\n        content: `确定要取消收藏这 ${this.selectedList.length} 个房源吗？`,\n        success: async (res) => {\n          if (res.confirm) {\n            try {\n              // 批量取消收藏\n              for (let houseId of this.selectedList) {\n                await houseAPI.unfavoriteHouse(houseId)\n              }\n              \n              // 从列表中移除\n              this.favoriteList = this.favoriteList.filter(\n                item => !this.selectedList.includes(item._id)\n              )\n              \n              this.selectedList = []\n              \n              uni.showToast({\n                title: '删除成功',\n                icon: 'success'\n              })\n              \n            } catch (error) {\n              console.error('批量删除失败：', error)\n              uni.showToast({\n                title: '删除失败',\n                icon: 'none'\n              })\n            }\n          }\n        }\n      })\n    },\n    \n    // 取消收藏\n    async removeFavorite(house) {\n      try {\n        await houseAPI.unfavoriteHouse(house._id)\n        \n        // 从列表中移除\n        const index = this.favoriteList.findIndex(item => item._id === house._id)\n        if (index > -1) {\n          this.favoriteList.splice(index, 1)\n        }\n        \n        // 从选中列表中移除\n        const selectedIndex = this.selectedList.indexOf(house._id)\n        if (selectedIndex > -1) {\n          this.selectedList.splice(selectedIndex, 1)\n        }\n        \n        uni.showToast({\n          title: '已取消收藏',\n          icon: 'none'\n        })\n        \n      } catch (error) {\n        console.error('取消收藏失败：', error)\n      }\n    },\n    \n    // 跳转到详情页\n    goToDetail(house) {\n      uni.navigateTo({\n        url: `/pages/house/detail?id=${house._id}`\n      })\n    },\n    \n    // 跳转到首页\n    goToHome() {\n      uni.switchTab({\n        url: '/pages/index/index'\n      })\n    },\n    \n    // 格式化时间\n    formatTime(timestamp) {\n      if (!timestamp) return ''\n      \n      const date = new Date(timestamp)\n      const now = new Date()\n      const diff = now - date\n      \n      if (diff < 60000) { // 1分钟内\n        return '刚刚收藏'\n      } else if (diff < 3600000) { // 1小时内\n        return Math.floor(diff / 60000) + '分钟前'\n      } else if (diff < 86400000) { // 1天内\n        return Math.floor(diff / 3600000) + '小时前'\n      } else if (diff < 2592000000) { // 30天内\n        return Math.floor(diff / 86400000) + '天前'\n      } else {\n        return date.toLocaleDateString()\n      }\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.favorite-page {\n  height: 100vh;\n  display: flex;\n  flex-direction: column;\n  background-color: $bg-color-page;\n}\n\n/* 顶部操作栏 */\n.header-bar {\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  padding: $spacing-md $spacing-lg;\n  background-color: #fff;\n  border-bottom: 1px solid $border-color-light;\n  \n  .select-all {\n    display: flex;\n    align-items: center;\n    \n    .select-text {\n      margin-left: $spacing-xs;\n      font-size: 28rpx;\n      color: $text-color-secondary;\n    }\n  }\n  \n  .selected-count {\n    font-size: 26rpx;\n    color: $text-color-tertiary;\n  }\n  \n  .batch-actions {\n    .batch-btn {\n      padding: $spacing-xs $spacing-md;\n      font-size: 26rpx;\n      border: none;\n      border-radius: $border-radius-base;\n      \n      &.delete-btn {\n        background-color: $error-color;\n        color: #fff;\n      }\n    }\n  }\n}\n\n/* 收藏列表 */\n.favorite-scroll {\n  flex: 1;\n}\n\n.favorite-list {\n  padding: $spacing-md;\n  \n  .favorite-item {\n    position: relative;\n    display: flex;\n    align-items: center;\n    background-color: #fff;\n    border-radius: $border-radius-large;\n    margin-bottom: $spacing-md;\n    overflow: hidden;\n    box-shadow: $box-shadow-light;\n    \n    .select-box {\n      width: 80rpx;\n      display: flex;\n      align-items: center;\n      justify-content: center;\n    }\n    \n    .house-image {\n      width: 200rpx;\n      height: 150rpx;\n      flex-shrink: 0;\n    }\n    \n    .house-info {\n      flex: 1;\n      padding: $spacing-md;\n      display: flex;\n      flex-direction: column;\n      justify-content: space-between;\n      \n      .house-title {\n        font-size: 28rpx;\n        font-weight: 600;\n        color: $text-color-primary;\n        margin-bottom: $spacing-xs;\n        overflow: hidden;\n        text-overflow: ellipsis;\n        white-space: nowrap;\n      }\n      \n      .house-desc {\n        font-size: 24rpx;\n        color: $text-color-tertiary;\n        margin-bottom: $spacing-xs;\n        overflow: hidden;\n        text-overflow: ellipsis;\n        white-space: nowrap;\n      }\n      \n      .house-tags {\n        margin-bottom: $spacing-sm;\n        \n        .tag {\n          display: inline-block;\n          padding: 4rpx 12rpx;\n          background-color: $bg-color-light;\n          color: $text-color-secondary;\n          font-size: 20rpx;\n          border-radius: 8rpx;\n          margin-right: $spacing-xs;\n        }\n      }\n      \n      .house-bottom {\n        display: flex;\n        justify-content: space-between;\n        align-items: flex-end;\n        \n        .price {\n          .price-num {\n            font-size: 28rpx;\n            font-weight: 600;\n            color: $error-color;\n          }\n          \n          .price-unit {\n            font-size: 22rpx;\n            color: $text-color-tertiary;\n          }\n        }\n        \n        .favorite-time {\n          font-size: 22rpx;\n          color: $text-color-quaternary;\n        }\n      }\n    }\n    \n    .action-btn {\n      width: 80rpx;\n      height: 80rpx;\n      display: flex;\n      align-items: center;\n      justify-content: center;\n    }\n  }\n}\n\n/* 加载状态 */\n.loading-more {\n  padding: $spacing-lg;\n}\n\n/* 空状态 */\n.empty-state {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  padding: 120rpx $spacing-lg;\n  \n  .empty-image {\n    width: 200rpx;\n    height: 200rpx;\n    margin-bottom: $spacing-lg;\n  }\n  \n  .empty-text {\n    font-size: 28rpx;\n    color: $text-color-secondary;\n    margin-bottom: $spacing-sm;\n  }\n  \n  .empty-desc {\n    font-size: 24rpx;\n    color: $text-color-tertiary;\n    margin-bottom: $spacing-lg;\n  }\n  \n  .browse-btn {\n    width: 200rpx;\n    height: 72rpx;\n    background-color: $primary-color;\n    color: #fff;\n    font-size: 26rpx;\n    border: none;\n    border-radius: $border-radius-base;\n  }\n}\n</style>\n", "import mod from \"-!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./favorite.vue?vue&type=style&index=0&id=069a0a8a&lang=scss&scoped=true&\"; export default mod; export * from \"-!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./favorite.vue?vue&type=style&index=0&id=069a0a8a&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754033370408\n      var cssReload = require(\"D:/web/wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}
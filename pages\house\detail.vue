<template>
  <view class="house-detail-page">
    <!-- 图片轮播 -->
    <view class="image-section">
      <swiper class="image-swiper" indicator-dots circular>
        <swiper-item v-for="(image, index) in houseDetail.images" :key="index">
          <image 
            class="house-image" 
            :src="image" 
            mode="aspectFill"
            @click="previewImage(index)"
          ></image>
        </swiper-item>
      </swiper>
      <view class="image-count">
        <text>{{ currentImageIndex + 1 }}/{{ houseDetail.images?.length || 0 }}</text>
      </view>
    </view>

    <!-- 房源信息 -->
    <view class="info-section">
      <view class="price-info">
        <text class="price">{{ houseDetail.price }}</text>
        <text class="unit">元/月</text>
        <view class="favorite-btn" @click="toggleFavorite">
          <uni-icons 
            :type="houseDetail.is_favorite ? 'heart-filled' : 'heart'" 
            size="24" 
            :color="houseDetail.is_favorite ? '#FF4D4F' : '#999'"
          ></uni-icons>
        </view>
      </view>
      
      <view class="title">{{ houseDetail.title }}</view>
      
      <view class="tags">
        <text class="tag" v-for="tag in houseDetail.tags" :key="tag">{{ tag }}</text>
      </view>
      
      <view class="location">
        <uni-icons type="location" size="16" color="#999"></uni-icons>
        <text class="address">{{ houseDetail.address }}</text>
      </view>
    </view>

    <!-- 房源配置 -->
    <view class="config-section">
      <view class="section-title">房源配置</view>
      <view class="config-grid">
        <view class="config-item">
          <text class="config-label">面积</text>
          <text class="config-value">{{ houseDetail.area }}㎡</text>
        </view>
        <view class="config-item">
          <text class="config-label">楼层</text>
          <text class="config-value">{{ houseDetail.floor }}</text>
        </view>
        <view class="config-item">
          <text class="config-label">朝向</text>
          <text class="config-value">{{ houseDetail.orientation }}</text>
        </view>
        <view class="config-item">
          <text class="config-label">装修</text>
          <text class="config-value">{{ houseDetail.decoration }}</text>
        </view>
      </view>
    </view>

    <!-- 房源描述 -->
    <view class="desc-section">
      <view class="section-title">房源描述</view>
      <text class="desc-content">{{ houseDetail.description }}</text>
    </view>

    <!-- 房东信息 -->
    <view class="owner-section">
      <view class="section-title">房东信息</view>
      <view class="owner-info">
        <image class="owner-avatar" :src="houseDetail.owner?.avatar || '/static/default-avatar.png'"></image>
        <view class="owner-details">
          <text class="owner-name">{{ houseDetail.owner?.nickname || '房东' }}</text>
          <text class="owner-desc">{{ houseDetail.owner?.description || '暂无介绍' }}</text>
        </view>
        <button class="contact-btn" @click="contactOwner">联系房东</button>
      </view>
    </view>

    <!-- 底部操作栏 -->
    <view class="bottom-bar">
      <button class="share-btn" @click="shareHouse">
        <uni-icons type="redo" size="20" color="#666"></uni-icons>
        <text>分享</text>
      </button>
      <button class="favorite-btn" @click="toggleFavorite">
        <uni-icons 
          :type="houseDetail.is_favorite ? 'heart-filled' : 'heart'" 
          size="20" 
          :color="houseDetail.is_favorite ? '#FF4D4F' : '#666'"
        ></uni-icons>
        <text>{{ houseDetail.is_favorite ? '已收藏' : '收藏' }}</text>
      </button>
      <button class="contact-btn" @click="contactOwner">立即联系</button>
    </view>

    <!-- 加载状态 -->
    <view class="loading-state" v-if="loading">
      <uni-load-more status="loading"></uni-load-more>
    </view>
  </view>
</template>

<script>
import { houseAPI } from '@/utils/request.js'

export default {
  data() {
    return {
      loading: true,
      houseId: '',
      currentImageIndex: 0,
      houseDetail: {
        images: [],
        tags: [],
        owner: {}
      }
    }
  },
  
  onLoad(options) {
    this.houseId = options.id
    if (this.houseId) {
      this.loadHouseDetail()
    }
  },
  
  onShareAppMessage() {
    return {
      title: this.houseDetail.title,
      path: `/pages/house/detail?id=${this.houseId}`,
      imageUrl: this.houseDetail.images[0]
    }
  },
  
  methods: {
    // 加载房源详情
    async loadHouseDetail() {
      try {
        this.loading = true
        const result = await houseAPI.getHouseDetail(this.houseId)
        this.houseDetail = result.data
        
        // 设置页面标题
        uni.setNavigationBarTitle({
          title: this.houseDetail.title
        })
        
      } catch (error) {
        console.error('加载房源详情失败：', error)
        uni.showToast({
          title: '加载失败',
          icon: 'none'
        })
        // 返回上一页
        setTimeout(() => {
          uni.navigateBack()
        }, 1500)
      } finally {
        this.loading = false
      }
    },
    
    // 预览图片
    previewImage(index) {
      this.currentImageIndex = index
      uni.previewImage({
        urls: this.houseDetail.images,
        current: index
      })
    },
    
    // 切换收藏状态
    async toggleFavorite() {
      try {
        if (this.houseDetail.is_favorite) {
          await houseAPI.unfavoriteHouse(this.houseId)
          this.houseDetail.is_favorite = false
          uni.showToast({
            title: '已取消收藏',
            icon: 'none'
          })
        } else {
          await houseAPI.favoriteHouse(this.houseId)
          this.houseDetail.is_favorite = true
          uni.showToast({
            title: '收藏成功',
            icon: 'none'
          })
        }
      } catch (error) {
        console.error('收藏操作失败：', error)
      }
    },
    
    // 联系房东
    contactOwner() {
      // 检查登录状态
      const token = uni.getStorageSync('token')
      if (!token) {
        uni.showModal({
          title: '提示',
          content: '请先登录后再联系房东',
          success: (res) => {
            if (res.confirm) {
              uni.navigateTo({
                url: '/pages/auth/login'
              })
            }
          }
        })
        return
      }
      
      // 拨打电话或跳转聊天页面
      if (this.houseDetail.owner?.phone) {
        uni.makePhoneCall({
          phoneNumber: this.houseDetail.owner.phone
        })
      } else {
        uni.showToast({
          title: '暂无联系方式',
          icon: 'none'
        })
      }
    },
    
    // 分享房源
    shareHouse() {
      // 触发小程序分享
      uni.showShareMenu({
        withShareTicket: true
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.house-detail-page {
  background-color: $bg-color-page;
  padding-bottom: 120rpx;
}

/* 图片轮播 */
.image-section {
  position: relative;
  
  .image-swiper {
    height: 500rpx;
    
    .house-image {
      width: 100%;
      height: 100%;
    }
  }
  
  .image-count {
    position: absolute;
    bottom: $spacing-md;
    right: $spacing-md;
    padding: 8rpx 16rpx;
    background-color: rgba(0, 0, 0, 0.5);
    color: #fff;
    font-size: 24rpx;
    border-radius: 16rpx;
  }
}

/* 房源信息 */
.info-section {
  background-color: #fff;
  padding: $spacing-lg;
  margin-bottom: $spacing-md;
  
  .price-info {
    display: flex;
    align-items: baseline;
    margin-bottom: $spacing-md;
    position: relative;
    
    .price {
      font-size: 48rpx;
      font-weight: 600;
      color: $error-color;
    }
    
    .unit {
      font-size: 28rpx;
      color: $text-color-tertiary;
      margin-left: $spacing-xs;
    }
    
    .favorite-btn {
      position: absolute;
      right: 0;
      width: 60rpx;
      height: 60rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      background-color: $bg-color-light;
      border-radius: 50%;
    }
  }
  
  .title {
    font-size: 36rpx;
    font-weight: 600;
    color: $text-color-primary;
    margin-bottom: $spacing-md;
    line-height: 1.4;
  }
  
  .tags {
    margin-bottom: $spacing-md;
    
    .tag {
      display: inline-block;
      padding: 8rpx 16rpx;
      background-color: $bg-color-light;
      color: $text-color-secondary;
      font-size: 24rpx;
      border-radius: 8rpx;
      margin-right: $spacing-sm;
    }
  }
  
  .location {
    display: flex;
    align-items: center;
    
    .address {
      margin-left: 8rpx;
      font-size: 28rpx;
      color: $text-color-secondary;
    }
  }
}

/* 房源配置 */
.config-section {
  background-color: #fff;
  padding: $spacing-lg;
  margin-bottom: $spacing-md;
  
  .section-title {
    font-size: 32rpx;
    font-weight: 600;
    color: $text-color-primary;
    margin-bottom: $spacing-lg;
  }
  
  .config-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: $spacing-lg;
    
    .config-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      
      .config-label {
        font-size: 28rpx;
        color: $text-color-secondary;
      }
      
      .config-value {
        font-size: 28rpx;
        color: $text-color-primary;
        font-weight: 500;
      }
    }
  }
}

/* 房源描述 */
.desc-section {
  background-color: #fff;
  padding: $spacing-lg;
  margin-bottom: $spacing-md;
  
  .section-title {
    font-size: 32rpx;
    font-weight: 600;
    color: $text-color-primary;
    margin-bottom: $spacing-lg;
  }
  
  .desc-content {
    font-size: 28rpx;
    color: $text-color-secondary;
    line-height: 1.6;
  }
}

/* 房东信息 */
.owner-section {
  background-color: #fff;
  padding: $spacing-lg;
  margin-bottom: $spacing-md;
  
  .section-title {
    font-size: 32rpx;
    font-weight: 600;
    color: $text-color-primary;
    margin-bottom: $spacing-lg;
  }
  
  .owner-info {
    display: flex;
    align-items: center;
    
    .owner-avatar {
      width: 80rpx;
      height: 80rpx;
      border-radius: 50%;
      margin-right: $spacing-md;
    }
    
    .owner-details {
      flex: 1;
      
      .owner-name {
        display: block;
        font-size: 30rpx;
        font-weight: 500;
        color: $text-color-primary;
        margin-bottom: 8rpx;
      }
      
      .owner-desc {
        font-size: 24rpx;
        color: $text-color-tertiary;
      }
    }
    
    .contact-btn {
      padding: $spacing-sm $spacing-md;
      background-color: $primary-color;
      color: #fff;
      font-size: 26rpx;
      border: none;
      border-radius: $border-radius-base;
    }
  }
}

/* 底部操作栏 */
.bottom-bar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  display: flex;
  background-color: #fff;
  border-top: 1px solid $border-color-light;
  padding: $spacing-md;
  z-index: 100;
  
  .share-btn,
  .favorite-btn {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    width: 120rpx;
    height: 80rpx;
    background-color: transparent;
    border: none;
    font-size: 22rpx;
    color: $text-color-secondary;
    margin-right: $spacing-md;
    
    text {
      margin-top: 4rpx;
    }
  }
  
  .contact-btn {
    flex: 1;
    height: 80rpx;
    background-color: $primary-color;
    color: #fff;
    font-size: 30rpx;
    border: none;
    border-radius: $border-radius-base;
  }
}

/* 加载状态 */
.loading-state {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 400rpx;
}
</style>

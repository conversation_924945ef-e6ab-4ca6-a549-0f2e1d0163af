@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 自定义项目样式变量 */
/* 主题色 */
/* 辅助色 */
/* 中性色 */
/* 背景色 */
/* 边框色 */
/* 阴影 */
/* 圆角 */
/* 间距 */
.search-page.data-v-55d1bcf0 {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: #F5F5F5;
}
/* 搜索栏 */
.search-bar.data-v-55d1bcf0 {
  display: flex;
  align-items: center;
  padding: 24rpx;
  background-color: #fff;
  border-bottom: 1px solid #E8E8E8;
}
.search-bar .search-input.data-v-55d1bcf0 {
  flex: 1;
  display: flex;
  align-items: center;
  height: 72rpx;
  padding: 0 24rpx;
  background-color: #FAFAFA;
  border-radius: 36rpx;
  margin-right: 24rpx;
}
.search-bar .search-input .input.data-v-55d1bcf0 {
  flex: 1;
  margin-left: 8rpx;
  font-size: 28rpx;
  color: #262626;
}
.search-bar .search-input .clear-btn.data-v-55d1bcf0 {
  width: 32rpx;
  height: 32rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}
.search-bar .cancel-btn.data-v-55d1bcf0 {
  font-size: 28rpx;
  color: #595959;
}
/* 搜索历史 */
.history-section.data-v-55d1bcf0 {
  background-color: #fff;
  padding: 32rpx;
  margin-bottom: 24rpx;
}
.history-section .section-header.data-v-55d1bcf0 {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24rpx;
}
.history-section .section-header .section-title.data-v-55d1bcf0 {
  font-size: 28rpx;
  font-weight: 600;
  color: #262626;
}
.history-section .section-header .clear-history.data-v-55d1bcf0 {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}
.history-section .history-list .history-item.data-v-55d1bcf0 {
  display: flex;
  align-items: center;
  padding: 16rpx 0;
  border-bottom: 1px solid #F0F0F0;
}
.history-section .history-list .history-item.data-v-55d1bcf0:last-child {
  border-bottom: none;
}
.history-section .history-list .history-item .history-text.data-v-55d1bcf0 {
  margin-left: 16rpx;
  font-size: 26rpx;
  color: #595959;
}
/* 热门搜索 */
.hot-section.data-v-55d1bcf0 {
  background-color: #fff;
  padding: 32rpx;
  margin-bottom: 24rpx;
}
.hot-section .section-title.data-v-55d1bcf0 {
  font-size: 28rpx;
  font-weight: 600;
  color: #262626;
  margin-bottom: 24rpx;
}
.hot-section .hot-list.data-v-55d1bcf0 {
  display: flex;
  flex-wrap: wrap;
  gap: 24rpx;
}
.hot-section .hot-list .hot-item.data-v-55d1bcf0 {
  padding: 16rpx 24rpx;
  background-color: #FAFAFA;
  color: #595959;
  font-size: 26rpx;
  border-radius: 8rpx;
}
/* 搜索建议 */
.suggest-section.data-v-55d1bcf0 {
  background-color: #fff;
}
.suggest-section .suggest-list .suggest-item.data-v-55d1bcf0 {
  display: flex;
  align-items: center;
  padding: 24rpx 32rpx;
  border-bottom: 1px solid #F0F0F0;
}
.suggest-section .suggest-list .suggest-item.data-v-55d1bcf0:last-child {
  border-bottom: none;
}
.suggest-section .suggest-list .suggest-item .suggest-text.data-v-55d1bcf0 {
  margin-left: 16rpx;
  font-size: 28rpx;
  color: #262626;
}
/* 搜索结果 */
.result-section.data-v-55d1bcf0 {
  flex: 1;
  display: flex;
  flex-direction: column;
}
.result-section .result-header.data-v-55d1bcf0 {
  background-color: #fff;
  padding: 24rpx 32rpx;
  border-bottom: 1px solid #E8E8E8;
}
.result-section .result-header .result-count.data-v-55d1bcf0 {
  font-size: 26rpx;
  color: #595959;
}
.result-section .result-scroll.data-v-55d1bcf0 {
  flex: 1;
}
.result-section .result-list.data-v-55d1bcf0 {
  padding: 24rpx;
}
.result-section .result-list .house-card.data-v-55d1bcf0 {
  margin-bottom: 24rpx;
}
/* 加载状态 */
.loading-more.data-v-55d1bcf0 {
  padding: 32rpx;
}
/* 空状态 */
.empty-state.data-v-55d1bcf0 {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 120rpx 32rpx;
}
.empty-state .empty-image.data-v-55d1bcf0 {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 32rpx;
}
.empty-state .empty-text.data-v-55d1bcf0 {
  font-size: 28rpx;
  color: #595959;
  margin-bottom: 16rpx;
}
.empty-state .empty-desc.data-v-55d1bcf0 {
  font-size: 24rpx;
  color: #8C8C8C;
}

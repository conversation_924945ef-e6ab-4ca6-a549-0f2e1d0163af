<view class="login-page data-v-cbd6070a"><view class="header-section data-v-cbd6070a"><image class="logo data-v-cbd6070a" src="/static/logo.png"></image><text class="app-name data-v-cbd6070a">找房租房</text><text class="slogan data-v-cbd6070a">让租房变得更简单</text></view><view class="login-section data-v-cbd6070a"><view class="login-title data-v-cbd6070a">欢迎使用</view><view class="login-desc data-v-cbd6070a">请使用微信授权登录</view><button class="wechat-login-btn data-v-cbd6070a" open-type="getUserInfo" disabled="{{logging}}" data-event-opts="{{[['getuserinfo',[['onGetUserInfo',['$event']]]]]}}" bindgetuserinfo="__e"><uni-icons vue-id="aee9d82a-1" type="weixin" size="24" color="#fff" class="data-v-cbd6070a" bind:__l="__l"></uni-icons><text class="btn-text data-v-cbd6070a">{{logging?'登录中...':'微信授权登录'}}</text></button><view class="phone-login-section data-v-cbd6070a"><view class="divider data-v-cbd6070a"><text class="divider-text data-v-cbd6070a">或</text></view><view class="phone-input data-v-cbd6070a"><input class="phone-field data-v-cbd6070a" placeholder="请输入手机号" type="number" maxlength="11" data-event-opts="{{[['input',[['__set_model',['','phoneNumber','$event',[]]]]]]}}" value="{{phoneNumber}}" bindinput="__e"/></view><view class="code-input data-v-cbd6070a"><input class="code-field data-v-cbd6070a" placeholder="请输入验证码" type="number" maxlength="6" data-event-opts="{{[['input',[['__set_model',['','verifyCode','$event',[]]]]]]}}" value="{{verifyCode}}" bindinput="__e"/><button class="send-code-btn data-v-cbd6070a" disabled="{{codeSending||countdown>0}}" data-event-opts="{{[['tap',[['sendCode',['$event']]]]]}}" bindtap="__e">{{''+(countdown>0?countdown+'s':'发送验证码')+''}}</button></view><button class="phone-login-btn data-v-cbd6070a" disabled="{{!canPhoneLogin||logging}}" data-event-opts="{{[['tap',[['phoneLogin',['$event']]]]]}}" bindtap="__e">手机号登录</button></view></view><view class="agreement-section data-v-cbd6070a"><view class="agreement-text data-v-cbd6070a"><text class="data-v-cbd6070a">登录即表示同意</text><text data-event-opts="{{[['tap',[['showUserAgreement',['$event']]]]]}}" class="link data-v-cbd6070a" bindtap="__e">《用户协议》</text><text class="data-v-cbd6070a">和</text><text data-event-opts="{{[['tap',[['showPrivacyPolicy',['$event']]]]]}}" class="link data-v-cbd6070a" bindtap="__e">《隐私政策》</text></view></view><view class="footer-section data-v-cbd6070a"><text class="footer-text data-v-cbd6070a">安全 · 便捷 · 可靠</text></view></view>
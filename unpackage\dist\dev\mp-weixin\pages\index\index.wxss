@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 自定义项目样式变量 */
/* 主题色 */
/* 辅助色 */
/* 中性色 */
/* 背景色 */
/* 边框色 */
/* 阴影 */
/* 圆角 */
/* 间距 */
.home-page.data-v-57280228 {
  background-color: #F5F5F5;
  min-height: 100vh;
}
/* 搜索栏 */
.search-bar.data-v-57280228 {
  display: flex;
  align-items: center;
  padding: 24rpx;
  background-color: #fff;
}
.search-bar .search-input.data-v-57280228 {
  flex: 1;
  display: flex;
  align-items: center;
  height: 72rpx;
  padding: 0 24rpx;
  background-color: #FAFAFA;
  border-radius: 36rpx;
  margin-right: 24rpx;
}
.search-bar .search-input .search-placeholder.data-v-57280228 {
  margin-left: 8rpx;
  color: #8C8C8C;
  font-size: 28rpx;
}
.search-bar .location-btn.data-v-57280228 {
  display: flex;
  align-items: center;
}
.search-bar .location-btn .location-text.data-v-57280228 {
  margin-left: 4rpx;
  color: #007AFF;
  font-size: 26rpx;
}
/* 轮播图 */
.banner-section.data-v-57280228 {
  margin: 24rpx;
  border-radius: 12rpx;
  overflow: hidden;
}
.banner-section .banner-swiper.data-v-57280228 {
  height: 320rpx;
}
.banner-section .banner-swiper .banner-image.data-v-57280228 {
  width: 100%;
  height: 100%;
}
/* 分类导航 */
.category-section.data-v-57280228 {
  margin: 24rpx;
  padding: 32rpx;
  background-color: #fff;
  border-radius: 12rpx;
}
.category-section .category-grid.data-v-57280228 {
  display: flex;
  justify-content: space-between;
}
.category-section .category-grid .category-item.data-v-57280228 {
  display: flex;
  flex-direction: column;
  align-items: center;
  flex: 1;
}
.category-section .category-grid .category-item .category-icon.data-v-57280228 {
  width: 88rpx;
  height: 88rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #FAFAFA;
  border-radius: 12rpx;
  margin-bottom: 16rpx;
}
.category-section .category-grid .category-item .category-text.data-v-57280228 {
  font-size: 26rpx;
  color: #595959;
}
/* 推荐房源 */
.recommend-section.data-v-57280228 {
  margin: 24rpx;
}
.recommend-section .section-header.data-v-57280228 {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24rpx;
}
.recommend-section .section-header .section-title.data-v-57280228 {
  font-size: 32rpx;
  font-weight: 600;
  color: #262626;
}
.recommend-section .section-header .more-btn.data-v-57280228 {
  display: flex;
  align-items: center;
}
.recommend-section .section-header .more-btn .more-text.data-v-57280228 {
  font-size: 26rpx;
  color: #8C8C8C;
  margin-right: 4rpx;
}
.recommend-section .house-list .house-item.data-v-57280228 {
  display: flex;
  background-color: #fff;
  border-radius: 12rpx;
  margin-bottom: 24rpx;
  overflow: hidden;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}
.recommend-section .house-list .house-item .house-image.data-v-57280228 {
  width: 240rpx;
  height: 180rpx;
  flex-shrink: 0;
}
.recommend-section .house-list .house-item .house-info.data-v-57280228 {
  flex: 1;
  padding: 24rpx;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}
.recommend-section .house-list .house-item .house-info .house-title.data-v-57280228 {
  font-size: 30rpx;
  font-weight: 600;
  color: #262626;
  margin-bottom: 8rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.recommend-section .house-list .house-item .house-info .house-desc.data-v-57280228 {
  font-size: 24rpx;
  color: #8C8C8C;
  margin-bottom: 8rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.recommend-section .house-list .house-item .house-info .house-tags.data-v-57280228 {
  margin-bottom: 16rpx;
}
.recommend-section .house-list .house-item .house-info .house-tags .tag.data-v-57280228 {
  display: inline-block;
  padding: 4rpx 12rpx;
  background-color: #FAFAFA;
  color: #595959;
  font-size: 20rpx;
  border-radius: 8rpx;
  margin-right: 8rpx;
}
.recommend-section .house-list .house-item .house-info .house-bottom.data-v-57280228 {
  display: flex;
  justify-content: space-between;
  align-items: flex-end;
}
.recommend-section .house-list .house-item .house-info .house-bottom .price .price-num.data-v-57280228 {
  font-size: 32rpx;
  font-weight: 600;
  color: #FF4D4F;
}
.recommend-section .house-list .house-item .house-info .house-bottom .price .price-unit.data-v-57280228 {
  font-size: 24rpx;
  color: #8C8C8C;
}
.recommend-section .house-list .house-item .house-info .house-bottom .location.data-v-57280228 {
  display: flex;
  align-items: center;
}
.recommend-section .house-list .house-item .house-info .house-bottom .location .location-text.data-v-57280228 {
  margin-left: 4rpx;
  font-size: 22rpx;
  color: #8C8C8C;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  max-width: 160rpx;
}
/* 加载更多 */
.loading-more.data-v-57280228 {
  padding: 32rpx;
}

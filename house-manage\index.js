'use strict';

const uniID = require('uni-id-common');

exports.main = async (event, context) => {
  console.log('house-manage 云函数被调用，参数：', event);
  
  const { action } = event;
  
  try {
    switch (action) {
      case 'publishHouse':
        return await publishHouse(event);
      case 'getHouseList':
        return await getHouseList(event);
      case 'getHouseDetail':
        return await getHouseDetail(event);
      case 'toggleFavorite':
        return await toggleFavorite(event);
      case 'getMyHouses':
        return await getMyHouses(event);
      case 'getMyFavorites':
        return await getMyFavorites(event);
      case 'updateHouse':
        return await updateHouse(event);
      case 'deleteHouse':
        return await deleteHouse(event);
      default:
        return {
          code: 1001,
          message: '不支持的操作类型',
          data: null
        };
    }
  } catch (error) {
    console.error('house-manage 云函数执行错误：', error);
    return {
      code: 1005,
      message: error.message || '操作失败',
      data: null
    };
  }
};

// 发布房源
async function publishHouse(event) {
  const {
    title, desc, images, location, price, type, config,
    contact, area, floor, orientation, decoration
  } = event;
  
  // 验证用户登录
  const uniIdIns = uniID.createInstance({ context: this });
  const checkTokenResult = await uniIdIns.checkToken(event.uniIdToken);
  
  if (checkTokenResult.errCode !== 0) {
    return {
      code: 1002,
      message: '用户未登录',
      data: null
    };
  }
  
  // 参数验证
  if (!title || !desc || !price || !location || !contact) {
    return {
      code: 1001,
      message: '缺少必要参数',
      data: null
    };
  }
  
  try {
    const db = uniCloud.database();
    const houseCollection = db.collection('house');
    
    const houseData = {
      title,
      desc,
      images: images || [],
      location,
      price: Number(price),
      type: type || '',
      config: config || [],
      contact,
      area: area || 0,
      floor: floor || '',
      orientation: orientation || '',
      decoration: decoration || '',
      owner_id: checkTokenResult.uid,
      status: 'pending', // 待审核
      reject_reason: '',
      created_at: new Date(),
      updated_at: new Date(),
      view_count: 0,
      is_featured: false
    };
    
    const result = await houseCollection.add(houseData);
    
    // 更新用户发布数量
    const userCollection = db.collection('uni-id-users');
    await userCollection.doc(checkTokenResult.uid).update({
      publish_count: db.command.inc(1)
    });
    
    return {
      code: 0,
      message: '发布成功',
      data: {
        house_id: result.id
      }
    };
    
  } catch (error) {
    console.error('发布房源错误：', error);
    return {
      code: 1005,
      message: '发布房源失败',
      data: null
    };
  }
}

// 获取房源列表
async function getHouseList(event) {
  const { page = 1, limit = 10, filters = {} } = event;
  
  try {
    const db = uniCloud.database();
    const houseCollection = db.collection('house');
    
    // 构建查询条件
    let whereCondition = {
      status: 'approved' // 只显示已审核通过的房源
    };
    
    // 添加筛选条件
    if (filters.city) {
      whereCondition['location.city'] = filters.city;
    }
    
    if (filters.district) {
      whereCondition['location.district'] = filters.district;
    }
    
    if (filters.priceMin || filters.priceMax) {
      whereCondition.price = {};
      if (filters.priceMin) {
        whereCondition.price[db.command.gte] = Number(filters.priceMin);
      }
      if (filters.priceMax) {
        whereCondition.price[db.command.lte] = Number(filters.priceMax);
      }
    }
    
    if (filters.type) {
      whereCondition.type = filters.type;
    }
    
    if (filters.keyword) {
      whereCondition[db.command.or] = [
        { title: new RegExp(filters.keyword, 'i') },
        { desc: new RegExp(filters.keyword, 'i') },
        { 'location.address': new RegExp(filters.keyword, 'i') }
      ];
    }
    
    // 查询总数
    const countResult = await houseCollection.where(whereCondition).count();
    
    // 查询列表数据
    const listResult = await houseCollection
      .where(whereCondition)
      .field({
        title: true,
        price: true,
        type: true,
        location: true,
        images: true,
        created_at: true,
        view_count: true,
        is_featured: true,
        area: true,
        config: true
      })
      .orderBy('is_featured', 'desc')
      .orderBy('created_at', 'desc')
      .skip((page - 1) * limit)
      .limit(limit)
      .get();
    
    return {
      code: 0,
      message: '获取成功',
      data: {
        list: listResult.data,
        total: countResult.total,
        page: Number(page),
        limit: Number(limit)
      }
    };
    
  } catch (error) {
    console.error('获取房源列表错误：', error);
    return {
      code: 1005,
      message: '获取房源列表失败',
      data: null
    };
  }
}

// 获取房源详情
async function getHouseDetail(event) {
  const { house_id } = event;
  
  if (!house_id) {
    return {
      code: 1001,
      message: '缺少房源ID',
      data: null
    };
  }
  
  try {
    const db = uniCloud.database();
    const houseCollection = db.collection('house');
    
    const result = await houseCollection.doc(house_id).get();
    
    if (result.data.length === 0) {
      return {
        code: 3001,
        message: '房源不存在',
        data: null
      };
    }
    
    const houseData = result.data[0];
    
    // 只有已审核通过的房源才能查看详情（除非是房源所有者）
    if (houseData.status !== 'approved') {
      // 验证是否为房源所有者
      const uniIdIns = uniID.createInstance({ context: this });
      const checkTokenResult = await uniIdIns.checkToken(event.uniIdToken);
      
      if (checkTokenResult.errCode !== 0 || checkTokenResult.uid !== houseData.owner_id) {
        return {
          code: 3002,
          message: '房源已下架',
          data: null
        };
      }
    }
    
    // 增加浏览次数
    await houseCollection.doc(house_id).update({
      view_count: db.command.inc(1)
    });
    
    return {
      code: 0,
      message: '获取成功',
      data: houseData
    };
    
  } catch (error) {
    console.error('获取房源详情错误：', error);
    return {
      code: 1005,
      message: '获取房源详情失败',
      data: null
    };
  }
}

// 收藏/取消收藏房源
async function toggleFavorite(event) {
  const { house_id } = event;
  
  if (!house_id) {
    return {
      code: 1001,
      message: '缺少房源ID',
      data: null
    };
  }
  
  // 验证用户登录
  const uniIdIns = uniID.createInstance({ context: this });
  const checkTokenResult = await uniIdIns.checkToken(event.uniIdToken);
  
  if (checkTokenResult.errCode !== 0) {
    return {
      code: 1002,
      message: '用户未登录',
      data: null
    };
  }
  
  try {
    const db = uniCloud.database();
    const userCollection = db.collection('uni-id-users');
    
    // 获取用户当前收藏列表
    const userResult = await userCollection.doc(checkTokenResult.uid).get();
    const userData = userResult.data[0];
    const favorites = userData.favorites || [];
    
    let isFavorited = false;
    let newFavorites = [];
    
    if (favorites.includes(house_id)) {
      // 取消收藏
      newFavorites = favorites.filter(id => id !== house_id);
      isFavorited = false;
    } else {
      // 添加收藏
      newFavorites = [...favorites, house_id];
      isFavorited = true;
    }
    
    // 更新用户收藏列表
    await userCollection.doc(checkTokenResult.uid).update({
      favorites: newFavorites
    });
    
    return {
      code: 0,
      message: '操作成功',
      data: {
        is_favorited: isFavorited
      }
    };

  } catch (error) {
    console.error('收藏操作错误：', error);
    return {
      code: 1005,
      message: '收藏操作失败',
      data: null
    };
  }
}

// 获取我的发布
async function getMyHouses(event) {
  const { page = 1, limit = 10 } = event;

  // 验证用户登录
  const uniIdIns = uniID.createInstance({ context: this });
  const checkTokenResult = await uniIdIns.checkToken(event.uniIdToken);

  if (checkTokenResult.errCode !== 0) {
    return {
      code: 1002,
      message: '用户未登录',
      data: null
    };
  }

  try {
    const db = uniCloud.database();
    const houseCollection = db.collection('house');

    const whereCondition = {
      owner_id: checkTokenResult.uid
    };

    // 查询总数
    const countResult = await houseCollection.where(whereCondition).count();

    // 查询列表数据
    const listResult = await houseCollection
      .where(whereCondition)
      .orderBy('created_at', 'desc')
      .skip((page - 1) * limit)
      .limit(limit)
      .get();

    return {
      code: 0,
      message: '获取成功',
      data: {
        list: listResult.data,
        total: countResult.total,
        page: Number(page),
        limit: Number(limit)
      }
    };

  } catch (error) {
    console.error('获取我的发布错误：', error);
    return {
      code: 1005,
      message: '获取我的发布失败',
      data: null
    };
  }
}

// 获取我的收藏
async function getMyFavorites(event) {
  const { page = 1, limit = 10 } = event;

  // 验证用户登录
  const uniIdIns = uniID.createInstance({ context: this });
  const checkTokenResult = await uniIdIns.checkToken(event.uniIdToken);

  if (checkTokenResult.errCode !== 0) {
    return {
      code: 1002,
      message: '用户未登录',
      data: null
    };
  }

  try {
    const db = uniCloud.database();
    const userCollection = db.collection('uni-id-users');
    const houseCollection = db.collection('house');

    // 获取用户收藏列表
    const userResult = await userCollection.doc(checkTokenResult.uid).get();
    const userData = userResult.data[0];
    const favorites = userData.favorites || [];

    if (favorites.length === 0) {
      return {
        code: 0,
        message: '获取成功',
        data: {
          list: [],
          total: 0,
          page: Number(page),
          limit: Number(limit)
        }
      };
    }

    // 分页处理收藏ID列表
    const startIndex = (page - 1) * limit;
    const endIndex = startIndex + limit;
    const paginatedFavorites = favorites.slice(startIndex, endIndex);

    // 查询收藏的房源详情
    const listResult = await houseCollection
      .where({
        _id: db.command.in(paginatedFavorites),
        status: 'approved'
      })
      .field({
        title: true,
        price: true,
        type: true,
        location: true,
        images: true,
        created_at: true,
        view_count: true,
        area: true,
        config: true
      })
      .get();

    return {
      code: 0,
      message: '获取成功',
      data: {
        list: listResult.data,
        total: favorites.length,
        page: Number(page),
        limit: Number(limit)
      }
    };

  } catch (error) {
    console.error('获取我的收藏错误：', error);
    return {
      code: 1005,
      message: '获取我的收藏失败',
      data: null
    };
  }
}

// 更新房源
async function updateHouse(event) {
  const { house_id, ...updateData } = event;

  if (!house_id) {
    return {
      code: 1001,
      message: '缺少房源ID',
      data: null
    };
  }

  // 验证用户登录
  const uniIdIns = uniID.createInstance({ context: this });
  const checkTokenResult = await uniIdIns.checkToken(event.uniIdToken);

  if (checkTokenResult.errCode !== 0) {
    return {
      code: 1002,
      message: '用户未登录',
      data: null
    };
  }

  try {
    const db = uniCloud.database();
    const houseCollection = db.collection('house');

    // 验证房源所有权
    const houseResult = await houseCollection.doc(house_id).get();
    if (houseResult.data.length === 0) {
      return {
        code: 3001,
        message: '房源不存在',
        data: null
      };
    }

    const houseData = houseResult.data[0];
    if (houseData.owner_id !== checkTokenResult.uid) {
      return {
        code: 1003,
        message: '权限不足',
        data: null
      };
    }

    // 准备更新数据
    const allowedFields = ['title', 'desc', 'images', 'location', 'price', 'type', 'config', 'contact', 'area', 'floor', 'orientation', 'decoration'];
    const finalUpdateData = {
      updated_at: new Date(),
      status: 'pending' // 更新后需要重新审核
    };

    allowedFields.forEach(field => {
      if (updateData[field] !== undefined) {
        finalUpdateData[field] = updateData[field];
      }
    });

    await houseCollection.doc(house_id).update(finalUpdateData);

    return {
      code: 0,
      message: '更新成功',
      data: null
    };

  } catch (error) {
    console.error('更新房源错误：', error);
    return {
      code: 1005,
      message: '更新房源失败',
      data: null
    };
  }
}

// 删除房源
async function deleteHouse(event) {
  const { house_id } = event;

  if (!house_id) {
    return {
      code: 1001,
      message: '缺少房源ID',
      data: null
    };
  }

  // 验证用户登录
  const uniIdIns = uniID.createInstance({ context: this });
  const checkTokenResult = await uniIdIns.checkToken(event.uniIdToken);

  if (checkTokenResult.errCode !== 0) {
    return {
      code: 1002,
      message: '用户未登录',
      data: null
    };
  }

  try {
    const db = uniCloud.database();
    const houseCollection = db.collection('house');

    // 验证房源所有权
    const houseResult = await houseCollection.doc(house_id).get();
    if (houseResult.data.length === 0) {
      return {
        code: 3001,
        message: '房源不存在',
        data: null
      };
    }

    const houseData = houseResult.data[0];
    if (houseData.owner_id !== checkTokenResult.uid) {
      return {
        code: 1003,
        message: '权限不足',
        data: null
      };
    }

    // 删除房源
    await houseCollection.doc(house_id).remove();

    // 更新用户发布数量
    const userCollection = db.collection('uni-id-users');
    await userCollection.doc(checkTokenResult.uid).update({
      publish_count: db.command.inc(-1)
    });

    return {
      code: 0,
      message: '删除成功',
      data: null
    };

  } catch (error) {
    console.error('删除房源错误：', error);
    return {
      code: 1005,
      message: '删除房源失败',
      data: null
    };
  }
}

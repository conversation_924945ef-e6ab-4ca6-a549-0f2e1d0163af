@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 自定义项目样式变量 */
/* 主题色 */
/* 辅助色 */
/* 中性色 */
/* 背景色 */
/* 边框色 */
/* 阴影 */
/* 圆角 */
/* 间距 */
.house-detail-page.data-v-4265f319 {
  background-color: #F5F5F5;
  padding-bottom: 120rpx;
}
/* 图片轮播 */
.image-section.data-v-4265f319 {
  position: relative;
}
.image-section .image-swiper.data-v-4265f319 {
  height: 500rpx;
}
.image-section .image-swiper .house-image.data-v-4265f319 {
  width: 100%;
  height: 100%;
}
.image-section .image-count.data-v-4265f319 {
  position: absolute;
  bottom: 24rpx;
  right: 24rpx;
  padding: 8rpx 16rpx;
  background-color: rgba(0, 0, 0, 0.5);
  color: #fff;
  font-size: 24rpx;
  border-radius: 16rpx;
}
/* 房源信息 */
.info-section.data-v-4265f319 {
  background-color: #fff;
  padding: 32rpx;
  margin-bottom: 24rpx;
}
.info-section .price-info.data-v-4265f319 {
  display: flex;
  align-items: baseline;
  margin-bottom: 24rpx;
  position: relative;
}
.info-section .price-info .price.data-v-4265f319 {
  font-size: 48rpx;
  font-weight: 600;
  color: #FF4D4F;
}
.info-section .price-info .unit.data-v-4265f319 {
  font-size: 28rpx;
  color: #8C8C8C;
  margin-left: 8rpx;
}
.info-section .price-info .favorite-btn.data-v-4265f319 {
  position: absolute;
  right: 0;
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #FAFAFA;
  border-radius: 50%;
}
.info-section .title.data-v-4265f319 {
  font-size: 36rpx;
  font-weight: 600;
  color: #262626;
  margin-bottom: 24rpx;
  line-height: 1.4;
}
.info-section .tags.data-v-4265f319 {
  margin-bottom: 24rpx;
}
.info-section .tags .tag.data-v-4265f319 {
  display: inline-block;
  padding: 8rpx 16rpx;
  background-color: #FAFAFA;
  color: #595959;
  font-size: 24rpx;
  border-radius: 8rpx;
  margin-right: 16rpx;
}
.info-section .location.data-v-4265f319 {
  display: flex;
  align-items: center;
}
.info-section .location .address.data-v-4265f319 {
  margin-left: 8rpx;
  font-size: 28rpx;
  color: #595959;
}
/* 房源配置 */
.config-section.data-v-4265f319 {
  background-color: #fff;
  padding: 32rpx;
  margin-bottom: 24rpx;
}
.config-section .section-title.data-v-4265f319 {
  font-size: 32rpx;
  font-weight: 600;
  color: #262626;
  margin-bottom: 32rpx;
}
.config-section .config-grid.data-v-4265f319 {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 32rpx;
}
.config-section .config-grid .config-item.data-v-4265f319 {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.config-section .config-grid .config-item .config-label.data-v-4265f319 {
  font-size: 28rpx;
  color: #595959;
}
.config-section .config-grid .config-item .config-value.data-v-4265f319 {
  font-size: 28rpx;
  color: #262626;
  font-weight: 500;
}
/* 房源描述 */
.desc-section.data-v-4265f319 {
  background-color: #fff;
  padding: 32rpx;
  margin-bottom: 24rpx;
}
.desc-section .section-title.data-v-4265f319 {
  font-size: 32rpx;
  font-weight: 600;
  color: #262626;
  margin-bottom: 32rpx;
}
.desc-section .desc-content.data-v-4265f319 {
  font-size: 28rpx;
  color: #595959;
  line-height: 1.6;
}
/* 房东信息 */
.owner-section.data-v-4265f319 {
  background-color: #fff;
  padding: 32rpx;
  margin-bottom: 24rpx;
}
.owner-section .section-title.data-v-4265f319 {
  font-size: 32rpx;
  font-weight: 600;
  color: #262626;
  margin-bottom: 32rpx;
}
.owner-section .owner-info.data-v-4265f319 {
  display: flex;
  align-items: center;
}
.owner-section .owner-info .owner-avatar.data-v-4265f319 {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  margin-right: 24rpx;
}
.owner-section .owner-info .owner-details.data-v-4265f319 {
  flex: 1;
}
.owner-section .owner-info .owner-details .owner-name.data-v-4265f319 {
  display: block;
  font-size: 30rpx;
  font-weight: 500;
  color: #262626;
  margin-bottom: 8rpx;
}
.owner-section .owner-info .owner-details .owner-desc.data-v-4265f319 {
  font-size: 24rpx;
  color: #8C8C8C;
}
.owner-section .owner-info .contact-btn.data-v-4265f319 {
  padding: 16rpx 24rpx;
  background-color: #007AFF;
  color: #fff;
  font-size: 26rpx;
  border: none;
  border-radius: 8rpx;
}
/* 底部操作栏 */
.bottom-bar.data-v-4265f319 {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  display: flex;
  background-color: #fff;
  border-top: 1px solid #E8E8E8;
  padding: 24rpx;
  z-index: 100;
}
.bottom-bar .share-btn.data-v-4265f319,
.bottom-bar .favorite-btn.data-v-4265f319 {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 120rpx;
  height: 80rpx;
  background-color: transparent;
  border: none;
  font-size: 22rpx;
  color: #595959;
  margin-right: 24rpx;
}
.bottom-bar .share-btn text.data-v-4265f319,
.bottom-bar .favorite-btn text.data-v-4265f319 {
  margin-top: 4rpx;
}
.bottom-bar .contact-btn.data-v-4265f319 {
  flex: 1;
  height: 80rpx;
  background-color: #007AFF;
  color: #fff;
  font-size: 30rpx;
  border: none;
  border-radius: 8rpx;
}
/* 加载状态 */
.loading-state.data-v-4265f319 {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 400rpx;
}

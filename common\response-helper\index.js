/**
 * 云函数响应处理公共模块
 * 统一处理响应格式和错误码
 */

// 错误码定义
const ERROR_CODES = {
  SUCCESS: 0,
  PARAM_ERROR: 1001,
  NOT_LOGIN: 1002,
  PERMISSION_DENIED: 1003,
  DATA_NOT_FOUND: 1004,
  OPERATION_FAILED: 1005,
  
  // 用户认证相关
  WECHAT_LOGIN_FAILED: 2001,
  INVALID_TOKEN: 2002,
  
  // 房源相关
  HOUSE_NOT_FOUND: 3001,
  HOUSE_OFFLINE: 3002,
  
  // 文件上传相关
  FILE_UPLOAD_FAILED: 4001,
  FILE_TYPE_NOT_SUPPORTED: 4002,
  
  // 管理员相关
  ADMIN_LOGIN_FAILED: 5001,
  ADMIN_PERMISSION_DENIED: 5002
};

// 错误信息映射
const ERROR_MESSAGES = {
  [ERROR_CODES.SUCCESS]: '操作成功',
  [ERROR_CODES.PARAM_ERROR]: '参数错误',
  [ERROR_CODES.NOT_LOGIN]: '用户未登录',
  [ERROR_CODES.PERMISSION_DENIED]: '权限不足',
  [ERROR_CODES.DATA_NOT_FOUND]: '数据不存在',
  [ERROR_CODES.OPERATION_FAILED]: '操作失败',
  
  [ERROR_CODES.WECHAT_LOGIN_FAILED]: '微信登录失败',
  [ERROR_CODES.INVALID_TOKEN]: 'token无效',
  
  [ERROR_CODES.HOUSE_NOT_FOUND]: '房源不存在',
  [ERROR_CODES.HOUSE_OFFLINE]: '房源已下架',
  
  [ERROR_CODES.FILE_UPLOAD_FAILED]: '文件上传失败',
  [ERROR_CODES.FILE_TYPE_NOT_SUPPORTED]: '文件格式不支持',
  
  [ERROR_CODES.ADMIN_LOGIN_FAILED]: '管理员账号密码错误',
  [ERROR_CODES.ADMIN_PERMISSION_DENIED]: '管理员权限不足'
};

/**
 * 创建成功响应
 * @param {*} data 响应数据
 * @param {string} message 响应消息
 * @returns {object} 响应对象
 */
function success(data = null, message = null) {
  return {
    code: ERROR_CODES.SUCCESS,
    message: message || ERROR_MESSAGES[ERROR_CODES.SUCCESS],
    data: data
  };
}

/**
 * 创建错误响应
 * @param {number} code 错误码
 * @param {string} message 错误消息
 * @param {*} data 响应数据
 * @returns {object} 响应对象
 */
function error(code, message = null, data = null) {
  return {
    code: code,
    message: message || ERROR_MESSAGES[code] || '未知错误',
    data: data
  };
}

/**
 * 参数验证
 * @param {object} params 参数对象
 * @param {array} required 必需参数列表
 * @returns {object|null} 验证失败返回错误响应，成功返回null
 */
function validateParams(params, required) {
  for (const field of required) {
    if (params[field] === undefined || params[field] === null || params[field] === '') {
      return error(ERROR_CODES.PARAM_ERROR, `缺少必要参数: ${field}`);
    }
  }
  return null;
}

/**
 * 异步函数错误处理包装器
 * @param {function} fn 异步函数
 * @returns {function} 包装后的函数
 */
function asyncHandler(fn) {
  return async function(event, context) {
    try {
      return await fn(event, context);
    } catch (err) {
      console.error('云函数执行错误：', err);
      return error(ERROR_CODES.OPERATION_FAILED, err.message || '操作失败');
    }
  };
}

/**
 * 分页参数处理
 * @param {object} params 参数对象
 * @returns {object} 处理后的分页参数
 */
function handlePagination(params) {
  const page = Math.max(1, parseInt(params.page) || 1);
  const limit = Math.min(50, Math.max(1, parseInt(params.limit) || 10));
  const skip = (page - 1) * limit;
  
  return { page, limit, skip };
}

/**
 * 格式化分页响应数据
 * @param {array} list 数据列表
 * @param {number} total 总数量
 * @param {number} page 当前页码
 * @param {number} limit 每页数量
 * @returns {object} 格式化后的响应数据
 */
function formatPaginationData(list, total, page, limit) {
  return {
    list: list || [],
    total: total || 0,
    page: page,
    limit: limit,
    pages: Math.ceil((total || 0) / limit)
  };
}

/**
 * 日期范围处理
 * @param {string} startDate 开始日期
 * @param {string} endDate 结束日期
 * @returns {object} 处理后的日期范围
 */
function handleDateRange(startDate, endDate) {
  const range = {};
  
  if (startDate) {
    range.start = new Date(startDate);
    range.start.setHours(0, 0, 0, 0);
  }
  
  if (endDate) {
    range.end = new Date(endDate);
    range.end.setHours(23, 59, 59, 999);
  }
  
  return range;
}

/**
 * 获取今日开始时间
 * @returns {Date} 今日开始时间
 */
function getTodayStart() {
  const today = new Date();
  today.setHours(0, 0, 0, 0);
  return today;
}

/**
 * 获取今日结束时间
 * @returns {Date} 今日结束时间
 */
function getTodayEnd() {
  const today = new Date();
  today.setHours(23, 59, 59, 999);
  return today;
}

/**
 * 数据脱敏处理
 * @param {object} data 原始数据
 * @param {array} sensitiveFields 敏感字段列表
 * @returns {object} 脱敏后的数据
 */
function maskSensitiveData(data, sensitiveFields = ['password', 'token']) {
  if (!data || typeof data !== 'object') {
    return data;
  }
  
  const maskedData = { ...data };
  
  sensitiveFields.forEach(field => {
    if (maskedData[field]) {
      delete maskedData[field];
    }
  });
  
  return maskedData;
}

module.exports = {
  ERROR_CODES,
  ERROR_MESSAGES,
  success,
  error,
  validateParams,
  asyncHandler,
  handlePagination,
  formatPaginationData,
  handleDateRange,
  getTodayStart,
  getTodayEnd,
  maskSensitiveData
};

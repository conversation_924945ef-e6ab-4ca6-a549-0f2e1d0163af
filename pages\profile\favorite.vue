<template>
  <view class="favorite-page">
    <!-- 顶部操作栏 -->
    <view class="header-bar" v-if="favoriteList.length > 0">
      <view class="select-all" @click="toggleSelectAll">
        <uni-icons 
          :type="isAllSelected ? 'checkbox-filled' : 'checkbox'" 
          size="18" 
          :color="isAllSelected ? '#007AFF' : '#999'"
        ></uni-icons>
        <text class="select-text">全选</text>
      </view>
      <view class="selected-count" v-if="selectedList.length > 0">
        已选择 {{ selectedList.length }} 个
      </view>
      <view class="batch-actions" v-if="selectedList.length > 0">
        <button class="batch-btn delete-btn" @click="batchDelete">删除</button>
      </view>
    </view>

    <!-- 收藏列表 -->
    <scroll-view 
      class="favorite-scroll" 
      scroll-y 
      @scrolltolower="loadMore"
      refresher-enabled
      @refresherrefresh="onRefresh"
      :refresher-triggered="refreshing"
    >
      <view class="favorite-list">
        <view 
          class="favorite-item" 
          v-for="house in favoriteList" 
          :key="house._id"
          @click="goToDetail(house)"
        >
          <view class="select-box" @click.stop="toggleSelect(house)">
            <uni-icons 
              :type="selectedList.includes(house._id) ? 'checkbox-filled' : 'checkbox'" 
              size="18" 
              :color="selectedList.includes(house._id) ? '#007AFF' : '#999'"
            ></uni-icons>
          </view>
          
          <image class="house-image" :src="house.images[0]" mode="aspectFill"></image>
          
          <view class="house-info">
            <view class="house-title">{{ house.title }}</view>
            <view class="house-desc">{{ house.description }}</view>
            <view class="house-tags" v-if="house.tags && house.tags.length > 0">
              <text class="tag" v-for="tag in house.tags.slice(0, 3)" :key="tag">{{ tag }}</text>
            </view>
            <view class="house-bottom">
              <view class="price">
                <text class="price-num">{{ house.price }}</text>
                <text class="price-unit">元/月</text>
              </view>
              <view class="favorite-time">
                {{ formatTime(house.favorite_time) }}
              </view>
            </view>
          </view>
          
          <view class="action-btn" @click.stop="removeFavorite(house)">
            <uni-icons type="heart-filled" size="20" color="#FF4D4F"></uni-icons>
          </view>
        </view>
      </view>

      <!-- 加载状态 -->
      <view class="loading-more" v-if="favoriteList.length > 0">
        <uni-load-more :status="loadingStatus"></uni-load-more>
      </view>

      <!-- 空状态 -->
      <view class="empty-state" v-if="!loading && favoriteList.length === 0">
        <image class="empty-image" src="/static/empty/no-favorite.png"></image>
        <text class="empty-text">还没有收藏过房源</text>
        <text class="empty-desc">去首页看看有什么好房源吧</text>
        <button class="browse-btn" @click="goToHome">去逛逛</button>
      </view>
    </scroll-view>
  </view>
</template>

<script>
import { houseAPI } from '@/utils/request.js'

export default {
  data() {
    return {
      loading: false,
      refreshing: false,
      loadingStatus: 'more',
      currentPage: 1,
      pageSize: 10,
      hasMore: true,
      
      favoriteList: [],
      selectedList: []
    }
  },
  
  computed: {
    isAllSelected() {
      return this.favoriteList.length > 0 && this.selectedList.length === this.favoriteList.length
    }
  },
  
  onLoad() {
    this.loadFavorites()
  },
  
  methods: {
    // 加载收藏列表
    async loadFavorites(refresh = false) {
      if (this.loading) return
      
      try {
        this.loading = true
        if (refresh) {
          this.currentPage = 1
          this.hasMore = true
          this.refreshing = true
          this.selectedList = []
        }
        
        const params = {
          page: this.currentPage,
          limit: this.pageSize
        }
        
        const result = await houseAPI.getMyFavorites(params)
        const newList = result.data.list || []
        
        if (refresh) {
          this.favoriteList = newList
        } else {
          this.favoriteList.push(...newList)
        }
        
        this.hasMore = newList.length === this.pageSize
        this.loadingStatus = this.hasMore ? 'more' : 'noMore'
        
      } catch (error) {
        console.error('加载收藏列表失败：', error)
        this.loadingStatus = 'error'
      } finally {
        this.loading = false
        this.refreshing = false
      }
    },
    
    // 加载更多
    loadMore() {
      if (this.hasMore && !this.loading) {
        this.currentPage++
        this.loadingStatus = 'loading'
        this.loadFavorites()
      }
    },
    
    // 下拉刷新
    onRefresh() {
      this.loadFavorites(true)
    },
    
    // 切换选择
    toggleSelect(house) {
      const index = this.selectedList.indexOf(house._id)
      if (index > -1) {
        this.selectedList.splice(index, 1)
      } else {
        this.selectedList.push(house._id)
      }
    },
    
    // 全选/取消全选
    toggleSelectAll() {
      if (this.isAllSelected) {
        this.selectedList = []
      } else {
        this.selectedList = this.favoriteList.map(item => item._id)
      }
    },
    
    // 批量删除
    batchDelete() {
      if (this.selectedList.length === 0) return
      
      uni.showModal({
        title: '确认删除',
        content: `确定要取消收藏这 ${this.selectedList.length} 个房源吗？`,
        success: async (res) => {
          if (res.confirm) {
            try {
              // 批量取消收藏
              for (let houseId of this.selectedList) {
                await houseAPI.unfavoriteHouse(houseId)
              }
              
              // 从列表中移除
              this.favoriteList = this.favoriteList.filter(
                item => !this.selectedList.includes(item._id)
              )
              
              this.selectedList = []
              
              uni.showToast({
                title: '删除成功',
                icon: 'success'
              })
              
            } catch (error) {
              console.error('批量删除失败：', error)
              uni.showToast({
                title: '删除失败',
                icon: 'none'
              })
            }
          }
        }
      })
    },
    
    // 取消收藏
    async removeFavorite(house) {
      try {
        await houseAPI.unfavoriteHouse(house._id)
        
        // 从列表中移除
        const index = this.favoriteList.findIndex(item => item._id === house._id)
        if (index > -1) {
          this.favoriteList.splice(index, 1)
        }
        
        // 从选中列表中移除
        const selectedIndex = this.selectedList.indexOf(house._id)
        if (selectedIndex > -1) {
          this.selectedList.splice(selectedIndex, 1)
        }
        
        uni.showToast({
          title: '已取消收藏',
          icon: 'none'
        })
        
      } catch (error) {
        console.error('取消收藏失败：', error)
      }
    },
    
    // 跳转到详情页
    goToDetail(house) {
      uni.navigateTo({
        url: `/pages/house/detail?id=${house._id}`
      })
    },
    
    // 跳转到首页
    goToHome() {
      uni.switchTab({
        url: '/pages/index/index'
      })
    },
    
    // 格式化时间
    formatTime(timestamp) {
      if (!timestamp) return ''
      
      const date = new Date(timestamp)
      const now = new Date()
      const diff = now - date
      
      if (diff < 60000) { // 1分钟内
        return '刚刚收藏'
      } else if (diff < 3600000) { // 1小时内
        return Math.floor(diff / 60000) + '分钟前'
      } else if (diff < 86400000) { // 1天内
        return Math.floor(diff / 3600000) + '小时前'
      } else if (diff < 2592000000) { // 30天内
        return Math.floor(diff / 86400000) + '天前'
      } else {
        return date.toLocaleDateString()
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.favorite-page {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: $bg-color-page;
}

/* 顶部操作栏 */
.header-bar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: $spacing-md $spacing-lg;
  background-color: #fff;
  border-bottom: 1px solid $border-color-light;
  
  .select-all {
    display: flex;
    align-items: center;
    
    .select-text {
      margin-left: $spacing-xs;
      font-size: 28rpx;
      color: $text-color-secondary;
    }
  }
  
  .selected-count {
    font-size: 26rpx;
    color: $text-color-tertiary;
  }
  
  .batch-actions {
    .batch-btn {
      padding: $spacing-xs $spacing-md;
      font-size: 26rpx;
      border: none;
      border-radius: $border-radius-base;
      
      &.delete-btn {
        background-color: $error-color;
        color: #fff;
      }
    }
  }
}

/* 收藏列表 */
.favorite-scroll {
  flex: 1;
}

.favorite-list {
  padding: $spacing-md;
  
  .favorite-item {
    position: relative;
    display: flex;
    align-items: center;
    background-color: #fff;
    border-radius: $border-radius-large;
    margin-bottom: $spacing-md;
    overflow: hidden;
    box-shadow: $box-shadow-light;
    
    .select-box {
      width: 80rpx;
      display: flex;
      align-items: center;
      justify-content: center;
    }
    
    .house-image {
      width: 200rpx;
      height: 150rpx;
      flex-shrink: 0;
    }
    
    .house-info {
      flex: 1;
      padding: $spacing-md;
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      
      .house-title {
        font-size: 28rpx;
        font-weight: 600;
        color: $text-color-primary;
        margin-bottom: $spacing-xs;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
      
      .house-desc {
        font-size: 24rpx;
        color: $text-color-tertiary;
        margin-bottom: $spacing-xs;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
      
      .house-tags {
        margin-bottom: $spacing-sm;
        
        .tag {
          display: inline-block;
          padding: 4rpx 12rpx;
          background-color: $bg-color-light;
          color: $text-color-secondary;
          font-size: 20rpx;
          border-radius: 8rpx;
          margin-right: $spacing-xs;
        }
      }
      
      .house-bottom {
        display: flex;
        justify-content: space-between;
        align-items: flex-end;
        
        .price {
          .price-num {
            font-size: 28rpx;
            font-weight: 600;
            color: $error-color;
          }
          
          .price-unit {
            font-size: 22rpx;
            color: $text-color-tertiary;
          }
        }
        
        .favorite-time {
          font-size: 22rpx;
          color: $text-color-quaternary;
        }
      }
    }
    
    .action-btn {
      width: 80rpx;
      height: 80rpx;
      display: flex;
      align-items: center;
      justify-content: center;
    }
  }
}

/* 加载状态 */
.loading-more {
  padding: $spacing-lg;
}

/* 空状态 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 120rpx $spacing-lg;
  
  .empty-image {
    width: 200rpx;
    height: 200rpx;
    margin-bottom: $spacing-lg;
  }
  
  .empty-text {
    font-size: 28rpx;
    color: $text-color-secondary;
    margin-bottom: $spacing-sm;
  }
  
  .empty-desc {
    font-size: 24rpx;
    color: $text-color-tertiary;
    margin-bottom: $spacing-lg;
  }
  
  .browse-btn {
    width: 200rpx;
    height: 72rpx;
    background-color: $primary-color;
    color: #fff;
    font-size: 26rpx;
    border: none;
    border-radius: $border-radius-base;
  }
}
</style>

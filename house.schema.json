{"bsonType": "object", "required": ["title", "desc", "price", "location", "owner_id", "status"], "properties": {"_id": {"description": "房源ID"}, "title": {"bsonType": "string", "title": "房源标题", "description": "房源标题，不能为空", "maxLength": 100}, "desc": {"bsonType": "string", "title": "房源描述", "description": "房源详细描述", "maxLength": 1000}, "images": {"bsonType": "array", "title": "房源图片", "description": "房源图片URL数组", "items": {"bsonType": "string"}, "maxItems": 10}, "location": {"bsonType": "object", "title": "位置信息", "required": ["address"], "properties": {"address": {"bsonType": "string", "title": "详细地址"}, "latitude": {"bsonType": "number", "title": "纬度"}, "longitude": {"bsonType": "number", "title": "经度"}, "city": {"bsonType": "string", "title": "城市"}, "district": {"bsonType": "string", "title": "区域"}}}, "price": {"bsonType": "number", "title": "租金", "description": "月租金，单位：元", "minimum": 0}, "type": {"bsonType": "string", "title": "房型", "description": "房屋类型", "enum": ["一居", "二居", "三居", "四居", "合租", "单间", "其他"]}, "config": {"bsonType": "array", "title": "房屋配置", "description": "房屋配置数组", "items": {"bsonType": "string"}}, "contact": {"bsonType": "object", "title": "联系方式", "required": ["phone"], "properties": {"phone": {"bsonType": "string", "title": "联系电话"}, "wechat": {"bsonType": "string", "title": "微信号"}, "name": {"bsonType": "string", "title": "联系人姓名"}}}, "area": {"bsonType": "number", "title": "面积", "description": "房屋面积，单位：平方米", "minimum": 0}, "floor": {"bsonType": "string", "title": "楼层信息"}, "orientation": {"bsonType": "string", "title": "朝向"}, "decoration": {"bsonType": "string", "title": "装修情况", "enum": ["毛坯", "简装", "精装", "豪装"]}, "owner_id": {"bsonType": "string", "title": "发布用户ID", "description": "房源发布者的用户ID"}, "status": {"bsonType": "string", "title": "审核状态", "description": "房源审核状态", "enum": ["pending", "approved", "rejected"], "default": "pending"}, "reject_reason": {"bsonType": "string", "title": "驳回原因", "description": "审核驳回时的原因说明"}, "created_at": {"bsonType": "date", "title": "创建时间"}, "updated_at": {"bsonType": "date", "title": "更新时间"}, "view_count": {"bsonType": "number", "title": "浏览次数", "minimum": 0, "default": 0}, "is_featured": {"bsonType": "bool", "title": "是否推荐", "default": false}}, "permission": {"read": "doc.status == 'approved' || doc.owner_id == auth.uid", "create": "auth.uid != null", "update": "doc.owner_id == auth.uid", "delete": "doc.owner_id == auth.uid"}}